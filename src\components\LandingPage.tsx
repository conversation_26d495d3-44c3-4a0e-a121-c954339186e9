import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowRight, Search, MapPin, Clock, Star, Truck, Users, ChefHat, Zap, Menu, X } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { customerService } from '@/services/customerService';

interface Restaurant {
  id: string;
  name: string;
  image: string;
  rating: number;
  deliveryTime: string;
  distance: string;
  cuisine: string;
  basePrice: number;
  deliveryFee: number;
  specialOffer?: string;
  menu: Array<{
    id: string;
    name: string;
    price: number;
    description: string;
    available: boolean;
  }>;
}

interface LandingPageProps {
  onNavigateToAuth: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onNavigateToAuth }) => {
  const { login } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [featuredMenus, setFeaturedMenus] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  useEffect(() => {
    loadLandingData();
  }, []);

  const loadLandingData = async () => {
    try {
      const restaurantData = await customerService.getRestaurants();
      setRestaurants(restaurantData);

      // Extract featured menus from all restaurants
      const allMenus = restaurantData.flatMap(restaurant =>
        restaurant.menu.map(item => ({
          ...item,
          restaurantName: restaurant.name,
          restaurantId: restaurant.id,
          deliveryTime: restaurant.deliveryTime,
          rating: restaurant.rating
        }))
      );

      // Get random featured items
      const shuffled = allMenus.sort(() => 0.5 - Math.random());
      setFeaturedMenus(shuffled.slice(0, 8));

    } catch (error) {
      console.error('Failed to load landing data:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredRestaurants = restaurants.filter(restaurant =>
    restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    restaurant.cuisine.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const handleGetStarted = () => {
    onNavigateToAuth();
  };

  const handleLoginClick = () => {
    onNavigateToAuth();
  };

  const handleRegisterClick = () => {
    onNavigateToAuth();
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      {/* Navigation Header */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">F</span>
              </div>
              <span className="text-xl font-bold text-gray-800">FoodFlow Connect</span>
            </div>

            <div className="hidden md:flex items-center gap-8">
              <button
                onClick={() => scrollToSection('restaurants')}
                className="text-gray-600 hover:text-orange-600 transition-colors"
              >
                Restoran
              </button>
              <button
                onClick={() => scrollToSection('menu')}
                className="text-gray-600 hover:text-orange-600 transition-colors"
              >
                Menu
              </button>
              <button
                onClick={() => scrollToSection('about')}
                className="text-gray-600 hover:text-orange-600 transition-colors"
              >
                Tentang
              </button>
              <button
                onClick={() => scrollToSection('contact')}
                className="text-gray-600 hover:text-orange-600 transition-colors"
              >
                Kontak
              </button>
            </div>

            <div className="flex items-center gap-3">
              <div className="hidden md:flex items-center gap-3">
                <Button
                  variant="ghost"
                  onClick={handleLoginClick}
                  className="text-gray-600 hover:text-orange-600"
                >
                  Masuk
                </Button>
                <Button
                  onClick={handleRegisterClick}
                  className="bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Daftar
                </Button>
              </div>

              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                className="md:hidden"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              >
                {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white border-t">
            <div className="px-6 py-4 space-y-4">
              <button
                onClick={() => {
                  scrollToSection('restaurants');
                  setIsMobileMenuOpen(false);
                }}
                className="block w-full text-left text-gray-600 hover:text-orange-600 transition-colors"
              >
                Restoran
              </button>
              <button
                onClick={() => {
                  scrollToSection('menu');
                  setIsMobileMenuOpen(false);
                }}
                className="block w-full text-left text-gray-600 hover:text-orange-600 transition-colors"
              >
                Menu
              </button>
              <button
                onClick={() => {
                  scrollToSection('about');
                  setIsMobileMenuOpen(false);
                }}
                className="block w-full text-left text-gray-600 hover:text-orange-600 transition-colors"
              >
                Tentang
              </button>
              <button
                onClick={() => {
                  scrollToSection('contact');
                  setIsMobileMenuOpen(false);
                }}
                className="block w-full text-left text-gray-600 hover:text-orange-600 transition-colors"
              >
                Kontak
              </button>
              <div className="pt-4 border-t space-y-2">
                <Button
                  variant="ghost"
                  onClick={handleLoginClick}
                  className="w-full justify-start text-gray-600 hover:text-orange-600"
                >
                  Masuk
                </Button>
                <Button
                  onClick={handleRegisterClick}
                  className="w-full bg-orange-500 hover:bg-orange-600 text-white"
                >
                  Daftar
                </Button>
              </div>
            </div>
          </div>
        )}
      </nav>

      {/* Hero Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-orange-500 to-red-600 text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-6 py-20">
          <div className="text-center">
            <h1 className="text-5xl md:text-7xl font-bold mb-6">
              FoodFlow Connect
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-3xl mx-auto">
              Nikmati makanan lezat dari restoran terbaik, diantar langsung ke pintu Anda dalam hitungan menit!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
              <Button
                size="lg"
                className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4 text-lg"
                onClick={handleGetStarted}
              >
                Mulai Pesan Sekarang
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <div className="flex items-center gap-2 text-orange-100">
                <Truck className="h-5 w-5" />
                <span>Gratis ongkir untuk pesanan pertama!</span>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              <div className="text-center">
                <div className="text-3xl font-bold">{restaurants.length}+</div>
                <div className="text-orange-200">Restoran Partner</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">50K+</div>
                <div className="text-orange-200">Pelanggan Puas</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">15 Min</div>
                <div className="text-orange-200">Rata-rata Pengiriman</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold">4.8★</div>
                <div className="text-orange-200">Rating Aplikasi</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Search Section */}
      <div className="max-w-7xl mx-auto px-6 py-12">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">Cari Makanan Favorit Anda</h2>
          <p className="text-gray-600 mb-8">Dari makanan tradisional hingga internasional, semua ada di sini!</p>

          <div className="max-w-2xl mx-auto relative">
            <Search className="absolute left-4 top-4 h-5 w-5 text-gray-400" />
            <Input
              placeholder="Cari restoran, makanan, atau masakan..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-12 h-14 text-lg border-2 border-orange-200 focus:border-orange-500"
            />
          </div>
        </div>

        {/* Featured Categories */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
          {[
            { name: 'Makanan Padang', icon: '🍛', color: 'bg-red-100 text-red-700' },
            { name: 'Fast Food', icon: '🍔', color: 'bg-yellow-100 text-yellow-700' },
            { name: 'Minuman', icon: '🥤', color: 'bg-blue-100 text-blue-700' },
            { name: 'Dessert', icon: '🍰', color: 'bg-pink-100 text-pink-700' }
          ].map((category, index) => (
            <Card key={index} className="cursor-pointer hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <div className={`w-16 h-16 rounded-full ${category.color} flex items-center justify-center mx-auto mb-3 text-2xl`}>
                  {category.icon}
                </div>
                <h3 className="font-semibold">{category.name}</h3>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Featured Menus */}
      <div id="menu" className="bg-white py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Menu Pilihan Hari Ini</h2>
            <p className="text-gray-600">Makanan terpopuler yang paling banyak dipesan</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredMenus.map((menu, index) => (
              <Card key={index} className="overflow-hidden hover:shadow-xl transition-shadow group">
                <div className="h-48 bg-gradient-to-br from-orange-200 to-red-200 relative">
                  <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-20 transition-opacity"></div>
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-orange-500">Populer</Badge>
                  </div>
                  <div className="absolute bottom-4 right-4 bg-white rounded-full p-2">
                    <ChefHat className="h-5 w-5 text-orange-600" />
                  </div>
                </div>
                <CardContent className="p-4">
                  <h3 className="font-bold text-lg mb-2">{menu.name}</h3>
                  <p className="text-sm text-gray-600 mb-2">{menu.restaurantName}</p>
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-bold text-orange-600">
                      {formatCurrency(menu.price)}
                    </span>
                    <div className="flex items-center gap-1">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm">{menu.rating}</span>
                    </div>
                  </div>
                  <Button
                    className="w-full mt-3 bg-orange-500 hover:bg-orange-600"
                    onClick={handleGetStarted}
                  >
                    Pesan Sekarang
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Restaurant Section */}
      <div id="restaurants" className="max-w-7xl mx-auto px-6 py-16">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-800 mb-4">Restoran Terpercaya</h2>
          <p className="text-gray-600">Partner terbaik dengan kualitas makanan yang terjamin</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredRestaurants.slice(0, 6).map((restaurant) => (
            <Card key={restaurant.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 group">
              <div className="h-48 bg-gradient-to-br from-orange-200 to-red-200 relative">
                <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-20 transition-opacity"></div>
                {restaurant.specialOffer && (
                  <Badge className="absolute top-4 left-4 bg-red-500">
                    {restaurant.specialOffer}
                  </Badge>
                )}
                <div className="absolute bottom-4 left-4 bg-white rounded-lg p-2">
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    <span className="font-semibold">{restaurant.rating}</span>
                  </div>
                </div>
              </div>

              <CardContent className="p-6">
                <h3 className="font-bold text-xl mb-2">{restaurant.name}</h3>
                <p className="text-gray-600 mb-3">{restaurant.cuisine}</p>

                <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    <span>{restaurant.deliveryTime}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin className="h-4 w-4" />
                    <span>{restaurant.distance}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Truck className="h-4 w-4" />
                    <span>{formatCurrency(restaurant.deliveryFee)}</span>
                  </div>
                </div>

                {/* Popular Menu Preview */}
                <div className="mb-4">
                  <h4 className="font-semibold mb-2">Menu Populer:</h4>
                  <div className="space-y-2">
                    {restaurant.menu.slice(0, 3).map((item, index) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span className="text-gray-700">{item.name}</span>
                        <span className="font-semibold text-orange-600">
                          {formatCurrency(item.price)}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                <Button
                  className="w-full bg-orange-500 hover:bg-orange-600"
                  onClick={handleGetStarted}
                >
                  Lihat Menu Lengkap
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredRestaurants.length > 6 && (
          <div className="text-center mt-12">
            <Button
              variant="outline"
              size="lg"
              onClick={handleGetStarted}
            >
              Lihat Semua Restoran ({filteredRestaurants.length})
            </Button>
          </div>
        )}
      </div>

      {/* Features Section */}
      <div id="about" className="bg-gray-50 py-16">
        <div className="max-w-7xl mx-auto px-6">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-4">Mengapa Pilih FoodFlow?</h2>
            <p className="text-gray-600">Pengalaman pesan makanan yang tak terlupakan</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-bold mb-2">Pengiriman Cepat</h3>
              <p className="text-gray-600">Makanan sampai dalam 15-30 menit dengan kualitas terjaga</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold mb-2">Partner Terpercaya</h3>
              <p className="text-gray-600">Restoran dan driver yang sudah terverifikasi dan berpengalaman</p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-bold mb-2">Kualitas Terbaik</h3>
              <p className="text-gray-600">Makanan berkualitas dengan rating tinggi dari pelanggan</p>
            </div>
          </div>
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-orange-500 to-red-600 text-white py-16">
        <div className="max-w-4xl mx-auto text-center px-6">
          <h2 className="text-4xl font-bold mb-4">Siap untuk Memesan?</h2>
          <p className="text-xl mb-8">Bergabunglah dengan ribuan pelanggan yang sudah merasakan kemudahan FoodFlow Connect</p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              className="bg-white text-orange-600 hover:bg-gray-100 px-8 py-4 text-lg"
              onClick={handleGetStarted}
            >
              Daftar Sebagai Customer
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-orange-600 px-8 py-4 text-lg"
              onClick={handleGetStarted}
            >
              Daftar Sebagai Merchant
            </Button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer id="contact" className="bg-gray-800 text-white py-12">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-2xl font-bold mb-4">FoodFlow Connect</h3>
              <p className="text-gray-400">Platform food delivery terpercaya untuk menghubungkan Anda dengan makanan lezat.</p>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Layanan</h4>
              <ul className="space-y-2 text-gray-400">
                <li><button onClick={handleGetStarted} className="hover:text-white transition-colors">Pesan Makanan</button></li>
                <li><button onClick={handleRegisterClick} className="hover:text-white transition-colors">Daftar Restoran</button></li>
                <li><button onClick={handleRegisterClick} className="hover:text-white transition-colors">Jadi Driver</button></li>
                <li><button onClick={handleGetStarted} className="hover:text-white transition-colors">Bantuan</button></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Perusahaan</h4>
              <ul className="space-y-2 text-gray-400">
                <li><button onClick={() => scrollToSection('about')} className="hover:text-white transition-colors">Tentang Kami</button></li>
                <li><button onClick={handleGetStarted} className="hover:text-white transition-colors">Karir</button></li>
                <li><button onClick={handleGetStarted} className="hover:text-white transition-colors">Blog</button></li>
                <li><button onClick={() => scrollToSection('contact')} className="hover:text-white transition-colors">Kontak</button></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold mb-4">Kontak</h4>
              <ul className="space-y-2 text-gray-400">
                <li>📞 +62 21 1234 5678</li>
                <li>✉️ <EMAIL></li>
                <li>📍 Jakarta, Indonesia</li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 FoodFlow Connect. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
