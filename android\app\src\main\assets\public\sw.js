if(!self.define){let e,i={};const s=(s,n)=>(s=new URL(s+".js",n).href,i[s]||new Promise((i=>{if("document"in self){const e=document.createElement("script");e.src=s,e.onload=i,document.head.appendChild(e)}else e=s,importScripts(s),i()})).then((()=>{let e=i[s];if(!e)throw new Error(`Module ${s} didn’t register its module`);return e})));self.define=(n,r)=>{const o=e||("document"in self?document.currentScript.src:"")||location.href;if(i[o])return;let t={};const c=e=>s(e,o),a={module:{uri:o},exports:t,require:c};i[o]=Promise.all(n.map((e=>a[e]||c(e)))).then((e=>(r(...e),t)))}}define(["./workbox-1ea6f077"],(function(e){"use strict";self.skipWaiting(),e.clientsClaim(),e.precacheAndRoute([{url:"assets/index-cevpKPCn.js",revision:null},{url:"assets/index-DzVj2faV.css",revision:null},{url:"favicon.ico",revision:"aa363275fcd8f6a482162da8f0de2087"},{url:"index.html",revision:"cee1957a26fb927bf6b5aeffbd1f6167"},{url:"placeholder.svg",revision:"35707bd9960ba5281c72af927b79291f"},{url:"registerSW.js",revision:"1872c500de691dce40960bb85481de07"},{url:"favicon.ico",revision:"aa363275fcd8f6a482162da8f0de2087"},{url:"manifest.webmanifest",revision:"5ad0e1691ba678bc29f1a55c1f89d2e0"}],{}),e.cleanupOutdatedCaches(),e.registerRoute(new e.NavigationRoute(e.createHandlerBoundToURL("index.html"))),e.registerRoute(/^https:\/\/localhost:5000\/api\//,new e.NetworkFirst({cacheName:"api-cache",plugins:[new e.ExpirationPlugin({maxEntries:100,maxAgeSeconds:86400})]}),"GET")}));
