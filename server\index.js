const express = require('express');
const cors = require('cors');
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');

dotenv.config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, '../public')));

// Basic route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to FoodFlow Connect API' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Mock database for development (when MySQL is not available)
let useMockData = true; // Use mock data for now since MySQL is not available

// Database connection
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  port: Number(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'foodflow_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Test database connection
pool.getConnection()
  .then(connection => {
    console.log('Database connected successfully');
    connection.release();
  })
  .catch(err => {
    console.error('Error connecting to the database:', err);
    console.log('Switching to mock data mode for development...');
    useMockData = true;
  });

// Mock data for development - Real data structure
const mockData = {
  users: [
    {
      id: 'merchant-1',
      name: 'Budi Santoso',
      email: '<EMAIL>',
      role: 'merchant',
      phone: '+62 811 3333 3333',
      status: 'active',
      business_name: 'Warung Padang Sederhana',
      business_address: 'Jl. Sudirman No. 123, Jakarta Pusat',
      created_at: '2024-01-01 08:00:00'
    },
    {
      id: 'customer-1',
      name: 'Siti Nurhaliza',
      email: '<EMAIL>',
      role: 'customer',
      phone: '+62 811 2222 2222',
      status: 'active',
      created_at: '2024-01-05 10:00:00'
    },
    {
      id: 'customer-2',
      name: 'Ahmad Rahman',
      email: '<EMAIL>',
      role: 'customer',
      phone: '+62 812 4444 4444',
      status: 'active',
      created_at: '2024-01-08 14:30:00'
    },
    {
      id: 'driver-1',
      name: 'Joko Widodo',
      email: '<EMAIL>',
      role: 'driver',
      phone: '+62 813 5555 5555',
      status: 'active',
      vehicle_type: 'motorcycle',
      vehicle_number: 'B 1234 ABC',
      created_at: '2024-01-03 09:00:00'
    },
    {
      id: 'admin-1',
      name: 'Administrator',
      email: '<EMAIL>',
      role: 'admin',
      phone: '+62 811 1111 1111',
      status: 'active',
      created_at: '2024-01-01 00:00:00'
    }
  ],
  transactions: [
    {
      id: 'trans-1',
      user_id: 'customer-1',
      merchant_id: 'merchant-1',
      driver_id: 'driver-1',
      total_amount: 50000,
      delivery_fee: 10000,
      status: 'delivered',
      payment_status: 'paid',
      rating: 4.5,
      delivery_address: 'Jl. Thamrin No. 45, Jakarta Pusat',
      created_at: '2024-01-15 10:30:00',
      updated_at: '2024-01-15 11:15:00',
      actual_delivery_time: '2024-01-15 11:15:00'
    },
    {
      id: 'trans-2',
      user_id: 'customer-2',
      merchant_id: 'merchant-1',
      driver_id: 'driver-1',
      total_amount: 75000,
      delivery_fee: 10000,
      status: 'delivered',
      payment_status: 'paid',
      rating: 4.8,
      delivery_address: 'Jl. Gatot Subroto No. 88, Jakarta Selatan',
      created_at: '2024-01-14 14:20:00',
      updated_at: '2024-01-14 15:10:00',
      actual_delivery_time: '2024-01-14 15:10:00'
    },
    {
      id: 'trans-3',
      user_id: 'customer-1',
      merchant_id: 'merchant-1',
      driver_id: 'driver-1',
      total_amount: 35000,
      delivery_fee: 8000,
      status: 'delivered',
      payment_status: 'paid',
      rating: 4.2,
      delivery_address: 'Jl. Thamrin No. 45, Jakarta Pusat',
      created_at: '2024-01-13 12:30:00',
      updated_at: '2024-01-13 13:20:00',
      actual_delivery_time: '2024-01-13 13:20:00'
    },
    {
      id: 'trans-4',
      user_id: 'customer-2',
      merchant_id: 'merchant-1',
      total_amount: 28000,
      delivery_fee: 8000,
      status: 'processing',
      payment_status: 'paid',
      delivery_address: 'Jl. Gatot Subroto No. 88, Jakarta Selatan',
      created_at: '2024-01-16 09:15:00',
      updated_at: '2024-01-16 09:15:00'
    }
  ],
  merchant_reviews: [
    {
      id: 'review-1',
      user_id: 'customer-1',
      merchant_id: 'merchant-1',
      transaction_id: 'trans-1',
      rating: 4.5,
      comment: 'Makanannya enak dan pelayanan cepat. Rendangnya sangat lezat!',
      food_rating: 4.5,
      service_rating: 4.0,
      delivery_rating: 5.0,
      created_at: '2024-01-15 11:30:00',
      customer_name: 'Siti Nurhaliza',
      customer_avatar: null,
      order_date: '2024-01-15 10:30:00'
    },
    {
      id: 'review-2',
      user_id: 'customer-2',
      merchant_id: 'merchant-1',
      transaction_id: 'trans-2',
      rating: 4.8,
      comment: 'Rendangnya mantap, bumbunya pas. Ayam gulainya juga enak banget!',
      food_rating: 5.0,
      service_rating: 4.5,
      delivery_rating: 4.5,
      created_at: '2024-01-14 15:30:00',
      customer_name: 'Ahmad Rahman',
      customer_avatar: null,
      order_date: '2024-01-14 14:20:00'
    },
    {
      id: 'review-3',
      user_id: 'customer-1',
      merchant_id: 'merchant-1',
      transaction_id: 'trans-3',
      rating: 4.2,
      comment: 'Pengiriman cepat, makanan masih hangat. Sate Padangnya juara!',
      food_rating: 4.0,
      service_rating: 4.5,
      delivery_rating: 4.0,
      created_at: '2024-01-13 13:45:00',
      customer_name: 'Siti Nurhaliza',
      customer_avatar: null,
      order_date: '2024-01-13 12:30:00'
    }
  ],
  notifications: [
    {
      id: 'notif-1',
      user_id: 'merchant-1',
      title: 'Pesanan Baru',
      message: 'Anda mendapat pesanan baru dari Ahmad Rahman',
      type: 'order',
      is_read: false,
      created_at: '2024-01-16 09:15:00'
    },
    {
      id: 'notif-2',
      user_id: 'merchant-1',
      title: 'Review Baru',
      message: 'Siti Nurhaliza memberikan review 4.2 bintang untuk pesanan Anda',
      type: 'system',
      is_read: false,
      created_at: '2024-01-13 13:45:00'
    },
    {
      id: 'notif-3',
      user_id: 'merchant-1',
      title: 'Promosi Berakhir',
      message: 'Promosi diskon 20% akan berakhir dalam 2 hari',
      type: 'promotion',
      is_read: true,
      created_at: '2024-01-12 08:00:00'
    },
    {
      id: 'notif-4',
      user_id: 'merchant-1',
      title: 'Pesanan Selesai',
      message: 'Pesanan dari Siti Nurhaliza telah selesai diantar',
      type: 'order',
      is_read: true,
      created_at: '2024-01-15 11:15:00'
    }
  ],
  menu_items: [
    {
      id: 'menu-1',
      merchant_id: 'merchant-1',
      name: 'Rendang Daging',
      price: 25000,
      description: 'Rendang daging sapi dengan bumbu tradisional Padang yang kaya rempah',
      image: null,
      is_available: true,
      created_at: '2024-01-01 08:00:00'
    },
    {
      id: 'menu-2',
      merchant_id: 'merchant-1',
      name: 'Ayam Gulai',
      price: 20000,
      description: 'Ayam gulai dengan santan kelapa dan bumbu kuning yang gurih',
      image: null,
      is_available: true,
      created_at: '2024-01-01 08:00:00'
    },
    {
      id: 'menu-3',
      merchant_id: 'merchant-1',
      name: 'Nasi Padang Komplit',
      price: 35000,
      description: 'Nasi putih dengan rendang, ayam gulai, sayur nangka, dan sambal',
      image: null,
      is_available: true,
      created_at: '2024-01-01 08:00:00'
    },
    {
      id: 'menu-4',
      merchant_id: 'merchant-1',
      name: 'Sate Padang',
      price: 18000,
      description: 'Sate daging sapi dengan kuah kacang khas Padang yang pedas',
      image: null,
      is_available: true,
      created_at: '2024-01-01 08:00:00'
    },
    {
      id: 'menu-5',
      merchant_id: 'merchant-1',
      name: 'Dendeng Balado',
      price: 28000,
      description: 'Dendeng daging sapi dengan sambal balado pedas dan gurih',
      image: null,
      is_available: true,
      created_at: '2024-01-01 08:00:00'
    },
    {
      id: 'menu-6',
      merchant_id: 'merchant-1',
      name: 'Es Teh Manis',
      price: 5000,
      description: 'Es teh manis segar untuk menemani makanan pedas',
      image: null,
      is_available: true,
      created_at: '2024-01-01 08:00:00'
    },
    {
      id: 'menu-7',
      merchant_id: 'merchant-1',
      name: 'Es Jeruk',
      price: 8000,
      description: 'Es jeruk segar dengan potongan jeruk asli',
      image: null,
      is_available: true,
      created_at: '2024-01-01 08:00:00'
    }
  ],
  menu_categories: [
    {
      id: 'cat-1',
      merchant_id: 'merchant-1',
      name: 'Makanan Utama',
      description: 'Menu makanan utama',
      image: null,
      is_active: true,
      created_at: new Date('2024-01-01T08:00:00Z')
    }
  ],
  operating_hours: [
    {
      id: 'hours-1',
      merchant_id: 'merchant-1',
      day_of_week: 'monday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-2',
      merchant_id: 'merchant-1',
      day_of_week: 'tuesday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-3',
      merchant_id: 'merchant-1',
      day_of_week: 'wednesday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-4',
      merchant_id: 'merchant-1',
      day_of_week: 'thursday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-5',
      merchant_id: 'merchant-1',
      day_of_week: 'friday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-6',
      merchant_id: 'merchant-1',
      day_of_week: 'saturday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-7',
      merchant_id: 'merchant-1',
      day_of_week: 'sunday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    }
  ],
  activity_logs: [
    {
      id: 'log-1',
      user_id: 'admin-1',
      action: 'USER_APPROVED',
      description: 'Approved merchant registration for Warung Padang Sederhana',
      created_at: '2024-01-01 08:30:00'
    },
    {
      id: 'log-2',
      user_id: 'merchant-1',
      action: 'MENU_ADDED',
      description: 'Added new menu item: Rendang Daging',
      created_at: '2024-01-01 09:00:00'
    },
    {
      id: 'log-3',
      user_id: 'customer-1',
      action: 'ORDER_PLACED',
      description: 'Placed order for Nasi Padang Komplit',
      created_at: '2024-01-15 10:30:00'
    },
    {
      id: 'log-4',
      user_id: 'driver-1',
      action: 'ORDER_DELIVERED',
      description: 'Delivered order trans-1 to customer',
      created_at: '2024-01-15 11:15:00'
    }
  ],
  system_settings: [
    {
      id: 1,
      price_markup_percentage: 5.0,
      delivery_base_rate: 8000,
      merchant_commission_percentage: 15.0,
      driver_commission_percentage: 20.0,
      updated_at: '2024-01-01 00:00:00'
    }
  ]
};

// API Routes
app.get('/api/admin/stats', async (req, res) => {
  try {
    console.log('Fetching dashboard stats...');

    // Use mock data if database is not available
    if (useMockData) {
      const totalUsers = mockData.users.length;
      const totalMerchants = mockData.users.filter(u => u.role === 'merchant' && u.status === 'active').length;
      const totalDrivers = mockData.users.filter(u => u.role === 'driver' && u.status === 'active').length;
      const totalTransactions = mockData.transactions.filter(t => t.status === 'delivered').length;
      const totalRevenue = mockData.transactions
        .filter(t => t.status === 'delivered')
        .reduce((sum, t) => sum + t.total_amount, 0);

      const response = {
        totalUsers: totalUsers,
        totalMerchants: totalMerchants,
        totalDrivers: totalDrivers,
        totalTransactions: totalTransactions,
        totalRevenue: totalRevenue
      };
      console.log('Admin stats response (mock):', response);
      res.json(response);
      return;
    }

    const [users] = await pool.query(`
      SELECT
        COUNT(*) as total_users,
        SUM(CASE WHEN role = 'merchant' THEN 1 ELSE 0 END) as total_merchants,
        SUM(CASE WHEN role = 'driver' THEN 1 ELSE 0 END) as total_drivers
      FROM users
      WHERE status = 'active'
    `);
    console.log('Users query result:', users[0]);

    const [revenue] = await pool.query(`
      SELECT
        COUNT(*) as total_transactions,
        COALESCE(SUM(total_amount), 0) as total_revenue
      FROM transactions
      WHERE status = 'delivered'
      AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    `);
    console.log('Revenue query result:', revenue[0]);

    const response = {
      totalUsers: users[0].total_users || 0,
      totalMerchants: users[0].total_merchants || 0,
      totalDrivers: users[0].total_drivers || 0,
      totalTransactions: revenue[0].total_transactions || 0,
      totalRevenue: revenue[0].total_revenue || 0
    };
    console.log('Sending response:', response);

    res.json(response);
  } catch (error) {
    console.error('Error in /api/admin/stats:', error);
    res.status(500).json({
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

app.get('/api/admin/users', async (req, res) => {
  try {
    const { search, role } = req.query;
    console.log('Fetching users with filters:', { search, role });

    // Use mock data if database is not available
    if (useMockData) {
      let users = [...mockData.users];

      // Apply search filter
      if (search) {
        users = users.filter(u =>
          u.name.toLowerCase().includes(search.toLowerCase()) ||
          u.email.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Apply role filter
      if (role && role !== 'all') {
        users = users.filter(u => u.role === role);
      }

      // Sort by created_at desc
      users.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      console.log('Found users (mock):', users.length);
      res.json(users);
      return;
    }

    let query = `
      SELECT * FROM users
      WHERE 1=1
    `;
    const params = [];

    if (search) {
      query += ` AND (name LIKE ? OR email LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }

    if (role && role !== 'all') {
      query += ` AND role = ?`;
      params.push(role);
    }

    query += ` ORDER BY created_at DESC`;

    const [users] = await pool.query(query, params);
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/admin/pending-users', async (req, res) => {
  try {
    const [users] = await pool.query(`
      SELECT * FROM users
      WHERE status = 'pending'
      ORDER BY created_at DESC
    `);
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/admin/approve-user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    await pool.query(`
      UPDATE users
      SET status = 'active'
      WHERE id = ?
    `, [userId]);

    await pool.query(`
      INSERT INTO activity_logs (id, user_id, action, description)
      VALUES (UUID(), ?, 'USER_APPROVED', 'User approved by admin')
    `, [userId]);

    res.json({ message: 'User approved successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/admin/reject-user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    await pool.query(`
      UPDATE users
      SET status = 'suspended'
      WHERE id = ?
    `, [userId]);

    await pool.query(`
      INSERT INTO activity_logs (id, user_id, action, description)
      VALUES (UUID(), ?, 'USER_REJECTED', 'User rejected by admin')
    `, [userId]);

    res.json({ message: 'User rejected successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/admin/suspend-user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    await pool.query(`
      UPDATE users
      SET status = 'suspended'
      WHERE id = ?
    `, [userId]);

    await pool.query(`
      INSERT INTO activity_logs (id, user_id, action, description)
      VALUES (UUID(), ?, 'USER_SUSPENDED', 'User suspended by admin')
    `, [userId]);

    res.json({ message: 'User suspended successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Driver API Routes
app.get('/api/driver/orders', async (req, res) => {
  try {
    const [orders] = await pool.query(`
      SELECT
        t.*,
        u.name as customer_name,
        u.phone as customer_phone,
        m.name as merchant_name,
        m.phone as merchant_phone,
        m.business_name
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users m ON t.merchant_id = m.id
      WHERE t.driver_id IS NULL
      AND t.status = 'pending'
      ORDER BY t.created_at DESC
    `);
    res.json(orders);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/driver/active-orders', async (req, res) => {
  try {
    const { driverId } = req.query;
    const [orders] = await pool.query(`
      SELECT
        t.*,
        u.name as customer_name,
        u.phone as customer_phone,
        m.name as merchant_name,
        m.phone as merchant_phone,
        m.business_name
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users m ON t.merchant_id = m.id
      WHERE t.driver_id = ?
      AND t.status IN ('processing', 'delivering')
      ORDER BY t.created_at DESC
    `, [driverId]);
    res.json(orders);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/driver/accept-order', async (req, res) => {
  try {
    const { orderId, driverId } = req.body;
    await pool.query(`
      UPDATE transactions
      SET driver_id = ?, status = 'processing'
      WHERE id = ? AND driver_id IS NULL
    `, [driverId, orderId]);

    await pool.query(`
      INSERT INTO activity_logs (id, user_id, action, description)
      VALUES (UUID(), ?, 'ORDER_ACCEPTED', 'Driver accepted order')
    `, [driverId]);

    res.json({ message: 'Order accepted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/driver/update-order-status', async (req, res) => {
  try {
    const { orderId, status } = req.body;
    await pool.query(`
      UPDATE transactions
      SET status = ?
      WHERE id = ?
    `, [status, orderId]);

    res.json({ message: 'Order status updated successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Merchant API Routes
app.get('/api/merchant/menu', async (req, res) => {
  try {
    const { merchantId } = req.query;

    // Use mock data if database is not available
    if (useMockData) {
      const menu = mockData.menu_items.filter(item => item.merchant_id === merchantId);
      res.json(menu);
      return;
    }

    const [menu] = await pool.query(`
      SELECT * FROM menu_items
      WHERE merchant_id = ?
      ORDER BY created_at DESC
    `, [merchantId]);
    res.json(menu);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/merchant/menu', async (req, res) => {
  try {
    const { merchantId, name, price, description, image } = req.body;
    const [result] = await pool.query(`
      INSERT INTO menu_items (id, merchant_id, name, price, description, image)
      VALUES (UUID(), ?, ?, ?, ?, ?)
    `, [merchantId, name, price, description, image]);
    res.json({ id: result.insertId, message: 'Menu item added successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/merchant/menu/:itemId', async (req, res) => {
  try {
    const { itemId } = req.params;
    const { name, price, description, image, is_available } = req.body;
    await pool.query(`
      UPDATE menu_items
      SET name = ?, price = ?, description = ?, image = ?, is_available = ?
      WHERE id = ?
    `, [name, price, description, image, is_available, itemId]);
    res.json({ message: 'Menu item updated successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/merchant/orders', async (req, res) => {
  try {
    const { merchantId } = req.query;

    // Use mock data if database is not available
    if (useMockData) {
      const orders = mockData.transactions
        .filter(t => t.merchant_id === merchantId)
        .map(t => ({
          ...t,
          customer_name: 'Demo Customer',
          customer_phone: '+62 811 2222 2222',
          driver_name: null,
          driver_phone: null
        }));
      res.json(orders);
      return;
    }

    const [orders] = await pool.query(`
      SELECT
        t.*,
        u.name as customer_name,
        u.phone as customer_phone,
        d.name as driver_name,
        d.phone as driver_phone
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users d ON t.driver_id = d.id
      WHERE t.merchant_id = ?
      ORDER BY t.created_at DESC
    `, [merchantId]);
    res.json(orders);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Menu Categories
app.get('/api/merchant/categories', async (req, res) => {
  try {
    const { merchantId } = req.query;

    // Use mock data if database is not available
    if (useMockData) {
      const categories = mockData.menu_categories.filter(cat => cat.merchant_id === merchantId);
      res.json(categories);
      return;
    }

    const [categories] = await pool.query(`
      SELECT * FROM menu_categories
      WHERE merchant_id = ?
      ORDER BY name ASC
    `, [merchantId]);
    res.json(categories);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/merchant/categories', async (req, res) => {
  try {
    const { merchantId, name, description, image } = req.body;
    const [result] = await pool.query(`
      INSERT INTO menu_categories (id, merchant_id, name, description, image)
      VALUES (UUID(), ?, ?, ?, ?)
    `, [merchantId, name, description, image]);
    res.json({ id: result.insertId, message: 'Category added successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/merchant/categories/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { name, description, image, is_active } = req.body;
    await pool.query(`
      UPDATE menu_categories
      SET name = ?, description = ?, image = ?, is_active = ?
      WHERE id = ?
    `, [name, description, image, is_active, categoryId]);
    res.json({ message: 'Category updated successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Operating Hours
app.get('/api/merchant/operating-hours', async (req, res) => {
  try {
    const { merchantId } = req.query;

    // Use mock data if database is not available
    if (useMockData) {
      const hours = mockData.operating_hours.filter(h => h.merchant_id === merchantId);
      res.json(hours);
      return;
    }

    const [hours] = await pool.query(`
      SELECT * FROM operating_hours
      WHERE merchant_id = ?
      ORDER BY day_of_week ASC
    `, [merchantId]);
    res.json(hours);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/merchant/operating-hours', async (req, res) => {
  try {
    const { merchantId, hours } = req.body;
    const connection = await pool.getConnection();
    await connection.beginTransaction();

    try {
      // Delete existing hours
      await connection.query(`
        DELETE FROM operating_hours
        WHERE merchant_id = ?
      `, [merchantId]);

      // Insert new hours
      for (const hour of hours) {
        await connection.query(`
          INSERT INTO operating_hours (
            id, merchant_id, day_of_week, open_time, close_time, is_closed
          ) VALUES (UUID(), ?, ?, ?, ?, ?)
        `, [merchantId, hour.day_of_week, hour.open_time, hour.close_time, hour.is_closed]);
      }

      await connection.commit();
      res.json({ message: 'Operating hours updated successfully' });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Reviews
app.get('/api/merchant/reviews', async (req, res) => {
  try {
    const { merchantId } = req.query;
    console.log('Fetching reviews for merchant:', merchantId);

    if (!merchantId) {
      return res.status(400).json({ error: 'Merchant ID is required' });
    }

    // Use mock data if database is not available
    if (useMockData) {
      console.log('Using mock data for reviews');

      // Check if merchant exists in mock data
      const merchant = mockData.users.find(u => u.id === merchantId && u.role === 'merchant');
      if (!merchant) {
        return res.status(404).json({ error: 'Merchant not found' });
      }

      // Filter reviews for this merchant
      const reviews = mockData.merchant_reviews.filter(r => r.merchant_id === merchantId);
      console.log('Found reviews:', reviews.length);
      res.json(reviews);
      return;
    }

    // First check if merchant exists
    const [merchant] = await pool.query(
      'SELECT id FROM users WHERE id = ? AND role = ?',
      [merchantId, 'merchant']
    );

    if (!merchant.length) {
      return res.status(404).json({ error: 'Merchant not found' });
    }

    const [reviews] = await pool.query(`
      SELECT
        mr.*,
        u.name as customer_name,
        u.avatar as customer_avatar,
        t.id as transaction_id,
        t.created_at as order_date
      FROM merchant_reviews mr
      LEFT JOIN users u ON mr.user_id = u.id
      LEFT JOIN transactions t ON mr.transaction_id = t.id
      WHERE mr.merchant_id = ?
      ORDER BY mr.created_at DESC
    `, [merchantId]);

    console.log('Found reviews:', reviews.length);
    res.json(reviews || []);
  } catch (error) {
    console.error('Error in /api/merchant/reviews:', error);
    console.error('Error details:', error.message, error.code, error.sqlMessage);
    res.status(500).json({
      error: 'Failed to fetch merchant reviews',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Customer API Routes
app.get('/api/customer/restaurants', async (req, res) => {
  try {
    const [restaurants] = await pool.query(`
      SELECT
        u.*,
        COUNT(DISTINCT t.id) as total_orders,
        AVG(t.rating) as average_rating
      FROM users u
      LEFT JOIN transactions t ON u.id = t.merchant_id
      WHERE u.role = 'merchant'
      AND u.status = 'active'
      GROUP BY u.id
      ORDER BY average_rating DESC
    `);
    res.json(restaurants);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/customer/restaurant/:merchantId/menu', async (req, res) => {
  try {
    const { merchantId } = req.params;
    const [menu] = await pool.query(`
      SELECT * FROM menu_items
      WHERE merchant_id = ?
      AND is_available = true
      ORDER BY created_at DESC
    `, [merchantId]);
    res.json(menu);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/customer/orders', async (req, res) => {
  try {
    const { userId, merchantId, items, totalAmount, deliveryFee, deliveryAddress } = req.body;

    // Start transaction
    const connection = await pool.getConnection();
    await connection.beginTransaction();

    try {
      // Create order
      const [orderResult] = await connection.query(`
        INSERT INTO transactions (
          id, user_id, merchant_id, total_amount, delivery_fee,
          delivery_address, status
        ) VALUES (UUID(), ?, ?, ?, ?, ?, 'pending')
      `, [userId, merchantId, totalAmount, deliveryFee, deliveryAddress]);

      const orderId = orderResult.insertId;

      // Add order items
      for (const item of items) {
        await connection.query(`
          INSERT INTO transaction_items (
            id, transaction_id, menu_name, quantity, price
          ) VALUES (UUID(), ?, ?, ?, ?)
        `, [orderId, item.name, item.quantity, item.price]);
      }

      await connection.commit();
      res.json({ orderId, message: 'Order created successfully' });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/customer/orders', async (req, res) => {
  try {
    const { userId } = req.query;
    const [orders] = await pool.query(`
      SELECT
        t.*,
        m.name as merchant_name,
        m.business_name,
        d.name as driver_name,
        d.phone as driver_phone
      FROM transactions t
      LEFT JOIN users m ON t.merchant_id = m.id
      LEFT JOIN users d ON t.driver_id = d.id
      WHERE t.user_id = ?
      ORDER BY t.created_at DESC
    `, [userId]);
    res.json(orders);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Admin API Routes
app.get('/api/admin/transactions', async (req, res) => {
  try {
    console.log('Fetching recent transactions...');

    // Use mock data if database is not available
    if (useMockData) {
      const transactions = mockData.transactions.map(t => {
        const customer = mockData.users.find(u => u.id === t.user_id);
        const merchant = mockData.users.find(u => u.id === t.merchant_id);
        const driver = mockData.users.find(u => u.id === t.driver_id);

        return {
          ...t,
          customer_name: customer ? customer.name : null,
          merchant_name: merchant ? merchant.name : null,
          business_name: merchant ? merchant.business_name : null,
          driver_name: driver ? driver.name : null
        };
      }).sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      console.log('Found transactions (mock):', transactions.length);
      res.json(transactions);
      return;
    }

    const [transactions] = await pool.query(`
      SELECT
        t.*,
        u.name as customer_name,
        m.name as merchant_name,
        d.name as driver_name
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users m ON t.merchant_id = m.id
      LEFT JOIN users d ON t.driver_id = d.id
      ORDER BY t.created_at DESC
      LIMIT 10
    `);
    console.log('Transactions query result:', transactions);
    res.json(transactions);
  } catch (error) {
    console.error('Error in /api/admin/transactions:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/admin/activity-logs', async (req, res) => {
  try {
    console.log('Fetching activity logs...');

    // Use mock data if database is not available
    if (useMockData) {
      const logs = mockData.activity_logs.map(log => {
        const user = mockData.users.find(u => u.id === log.user_id);
        return {
          ...log,
          user_name: user ? user.name : null,
          user_role: user ? user.role : null
        };
      }).sort((a, b) => new Date(b.created_at) - new Date(a.created_at));

      console.log('Found activity logs (mock):', logs.length);
      res.json(logs);
      return;
    }

    const [logs] = await pool.query(`
      SELECT
        al.*,
        u.name as user_name,
        u.role as user_role
      FROM activity_logs al
      LEFT JOIN users u ON al.user_id = u.id
      ORDER BY al.created_at DESC
      LIMIT 10
    `);
    console.log('Activity logs query result:', logs);
    res.json(logs);
  } catch (error) {
    console.error('Error in /api/admin/activity-logs:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/admin/system-settings', async (req, res) => {
  try {
    console.log('Fetching system settings...');

    // Use mock data if database is not available
    if (useMockData) {
      const settings = mockData.system_settings[0] || {
        price_markup_percentage: 5.0,
        delivery_base_rate: 8000,
        merchant_commission_percentage: 15.0,
        driver_commission_percentage: 20.0
      };
      console.log('System settings (mock):', settings);
      res.json(settings);
      return;
    }

    const [settings] = await pool.query(`
      SELECT * FROM system_settings
      ORDER BY id DESC
      LIMIT 1
    `);
    console.log('System settings query result:', settings[0]);
    res.json(settings[0] || {
      price_markup_percentage: 0,
      delivery_base_rate: 0,
      merchant_commission_percentage: 0,
      driver_commission_percentage: 0
    });
  } catch (error) {
    console.error('Error in /api/admin/system-settings:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update System Settings
app.put('/api/admin/system-settings', async (req, res) => {
  try {
    console.log('Updating system settings:', req.body);

    // Use mock data if database is not available
    if (useMockData) {
      // Update mock data
      const newSettings = req.body;
      mockData.system_settings[0] = {
        ...mockData.system_settings[0],
        ...newSettings,
        updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
      };

      console.log('Updated system settings (mock):', mockData.system_settings[0]);
      res.json(mockData.system_settings[0]);
      return;
    }

    // Update database
    const { price_markup_percentage, delivery_base_rate, merchant_commission_percentage, driver_commission_percentage } = req.body;

    await pool.query(`
      UPDATE system_settings
      SET price_markup_percentage = ?,
          delivery_base_rate = ?,
          merchant_commission_percentage = ?,
          driver_commission_percentage = ?,
          updated_at = NOW()
      WHERE id = 1
    `, [price_markup_percentage, delivery_base_rate, merchant_commission_percentage, driver_commission_percentage]);

    // Get updated settings
    const [settings] = await pool.query(`
      SELECT * FROM system_settings
      ORDER BY id DESC
      LIMIT 1
    `);

    console.log('Updated system settings:', settings[0]);
    res.json(settings[0]);
  } catch (error) {
    console.error('Error in /api/admin/system-settings PUT:', error);
    res.status(500).json({ error: error.message });
  }
});

// Export Data Endpoint
app.get('/api/admin/export', async (req, res) => {
  try {
    console.log('Exporting admin data...');

    // Use mock data if database is not available
    if (useMockData) {
      const exportData = {
        stats: {
          totalUsers: mockData.users.length,
          totalMerchants: mockData.users.filter(u => u.role === 'merchant' && u.status === 'active').length,
          totalDrivers: mockData.users.filter(u => u.role === 'driver' && u.status === 'active').length,
          totalTransactions: mockData.transactions.filter(t => t.status === 'delivered').length,
          totalRevenue: mockData.transactions
            .filter(t => t.status === 'delivered')
            .reduce((sum, t) => sum + t.total_amount, 0)
        },
        users: mockData.users,
        transactions: mockData.transactions.map(t => {
          const customer = mockData.users.find(u => u.id === t.user_id);
          const merchant = mockData.users.find(u => u.id === t.merchant_id);
          const driver = mockData.users.find(u => u.id === t.driver_id);
          return {
            ...t,
            customer_name: customer ? customer.name : null,
            merchant_name: merchant ? merchant.name : null,
            driver_name: driver ? driver.name : null
          };
        }),
        activityLogs: mockData.activity_logs.map(log => {
          const user = mockData.users.find(u => u.id === log.user_id);
          return {
            ...log,
            user_name: user ? user.name : null,
            user_role: user ? user.role : null
          };
        }),
        systemSettings: mockData.system_settings[0],
        exportDate: new Date().toISOString(),
        exportedBy: 'Admin'
      };

      console.log('Export data prepared (mock)');
      res.json(exportData);
      return;
    }

    // Get data from database
    const [stats] = await pool.query(`
      SELECT
        COUNT(*) as total_users,
        SUM(CASE WHEN role = 'merchant' THEN 1 ELSE 0 END) as total_merchants,
        SUM(CASE WHEN role = 'driver' THEN 1 ELSE 0 END) as total_drivers
      FROM users
      WHERE status = 'active'
    `);

    const [revenue] = await pool.query(`
      SELECT
        COUNT(*) as total_transactions,
        COALESCE(SUM(total_amount), 0) as total_revenue
      FROM transactions
      WHERE status = 'delivered'
    `);

    const [users] = await pool.query(`SELECT * FROM users ORDER BY created_at DESC`);

    const [transactions] = await pool.query(`
      SELECT
        t.*,
        u.name as customer_name,
        m.name as merchant_name,
        d.name as driver_name
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users m ON t.merchant_id = m.id
      LEFT JOIN users d ON t.driver_id = d.id
      ORDER BY t.created_at DESC
    `);

    const [activityLogs] = await pool.query(`
      SELECT
        al.*,
        u.name as user_name,
        u.role as user_role
      FROM activity_logs al
      LEFT JOIN users u ON al.user_id = u.id
      ORDER BY al.created_at DESC
    `);

    const [settings] = await pool.query(`
      SELECT * FROM system_settings
      ORDER BY id DESC
      LIMIT 1
    `);

    const exportData = {
      stats: {
        totalUsers: stats[0].total_users || 0,
        totalMerchants: stats[0].total_merchants || 0,
        totalDrivers: stats[0].total_drivers || 0,
        totalTransactions: revenue[0].total_transactions || 0,
        totalRevenue: revenue[0].total_revenue || 0
      },
      users: users,
      transactions: transactions,
      activityLogs: activityLogs,
      systemSettings: settings[0] || {},
      exportDate: new Date().toISOString(),
      exportedBy: 'Admin'
    };

    console.log('Export data prepared from database');
    res.json(exportData);
  } catch (error) {
    console.error('Error in /api/admin/export:', error);
    res.status(500).json({ error: error.message });
  }
});

// Update Merchant Profile
app.put('/api/merchant/profile', async (req, res) => {
  try {
    const { merchantId, business_name, business_address, phone, email } = req.body;
    console.log('Updating merchant profile:', { merchantId, business_name, business_address, phone, email });

    // Use mock data if database is not available
    if (useMockData) {
      // Find and update merchant in mock data
      const merchantIndex = mockData.users.findIndex(u => u.id === merchantId && u.role === 'merchant');
      if (merchantIndex !== -1) {
        mockData.users[merchantIndex] = {
          ...mockData.users[merchantIndex],
          business_name: business_name || mockData.users[merchantIndex].business_name,
          business_address: business_address || mockData.users[merchantIndex].business_address,
          phone: phone || mockData.users[merchantIndex].phone,
          email: email || mockData.users[merchantIndex].email,
          updated_at: new Date().toISOString().slice(0, 19).replace('T', ' ')
        };

        console.log('Updated merchant profile (mock):', mockData.users[merchantIndex]);
        res.json(mockData.users[merchantIndex]);
        return;
      } else {
        res.status(404).json({ error: 'Merchant not found' });
        return;
      }
    }

    // Update database
    await pool.query(`
      UPDATE users
      SET business_name = ?,
          business_address = ?,
          phone = ?,
          email = ?,
          updated_at = NOW()
      WHERE id = ? AND role = 'merchant'
    `, [business_name, business_address, phone, email, merchantId]);

    // Get updated merchant data
    const [merchant] = await pool.query(`
      SELECT * FROM users WHERE id = ? AND role = 'merchant'
    `, [merchantId]);

    if (merchant.length === 0) {
      res.status(404).json({ error: 'Merchant not found' });
      return;
    }

    console.log('Updated merchant profile:', merchant[0]);
    res.json(merchant[0]);
  } catch (error) {
    console.error('Error in /api/merchant/profile PUT:', error);
    res.status(500).json({ error: error.message });
  }
});

// Merchant Analytics Routes
app.get('/api/merchant/analytics', async (req, res) => {
  try {
    const { merchantId } = req.query;
    console.log('Fetching merchant analytics for:', merchantId);

    // Use mock data if database is not available
    if (useMockData) {
      // Calculate real analytics from mock data
      const merchantTransactions = mockData.transactions.filter(t => t.merchant_id === merchantId);
      const deliveredTransactions = merchantTransactions.filter(t => t.status === 'delivered');

      const totalOrders = deliveredTransactions.length;
      const totalRevenue = deliveredTransactions.reduce((sum, t) => sum + t.total_amount, 0);

      // Mock popular items based on transaction data
      const popularItems = [
        { name: 'Nasi Padang Komplit', order_count: 2 },
        { name: 'Rendang Daging', order_count: 2 },
        { name: 'Ayam Gulai', order_count: 2 },
        { name: 'Sate Padang', order_count: 1 },
        { name: 'Dendeng Balado', order_count: 1 }
      ];

      // Calculate average preparation time from delivered orders
      let totalPrepTime = 0;
      let prepTimeCount = 0;
      deliveredTransactions.forEach(t => {
        if (t.actual_delivery_time && t.created_at) {
          const created = new Date(t.created_at);
          const delivered = new Date(t.actual_delivery_time);
          const prepTime = (delivered - created) / (1000 * 60); // minutes
          totalPrepTime += prepTime;
          prepTimeCount++;
        }
      });

      const avgPrepTime = prepTimeCount > 0 ? Math.round(totalPrepTime / prepTimeCount) : 45;

      const response = {
        totalOrders: totalOrders,
        orderChange: '+15.0',
        monthlyRevenue: totalRevenue,
        revenueChange: '+22.0',
        popularItems: popularItems,
        avgPreparationTime: avgPrepTime,
        prepTimeChange: '-2.0'
      };
      console.log('Merchant analytics response (calculated from mock data):', response);
      res.json(response);
      return;
    }

    // Get total orders and revenue for last 30 days
    const [monthlyStats] = await pool.query(`
      SELECT
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(AVG(TIMESTAMPDIFF(MINUTE, created_at, updated_at)), 0) as avg_preparation_time
      FROM transactions
      WHERE merchant_id = ?
      AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      AND status = 'delivered'
    `, [merchantId]);

    // Get previous month stats for comparison
    const [previousMonthStats] = await pool.query(`
      SELECT
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(AVG(TIMESTAMPDIFF(MINUTE, created_at, updated_at)), 0) as avg_preparation_time
      FROM transactions
      WHERE merchant_id = ?
      AND created_at >= DATE_SUB(NOW(), INTERVAL 60 DAY)
      AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
      AND status = 'delivered'
    `, [merchantId]);

    // Get most popular menu items
    const [popularItems] = await pool.query(`
      SELECT
        mi.name,
        COUNT(ti.id) as order_count
      FROM transaction_items ti
      JOIN transactions t ON ti.transaction_id = t.id
      JOIN menu_items mi ON ti.menu_name = mi.name
      WHERE t.merchant_id = ?
      AND t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY mi.name
      ORDER BY order_count DESC
      LIMIT 5
    `, [merchantId]);

    // Calculate percentage changes
    const orderChange = previousMonthStats[0].total_orders > 0
      ? ((monthlyStats[0].total_orders - previousMonthStats[0].total_orders) / previousMonthStats[0].total_orders) * 100
      : 0;

    const revenueChange = previousMonthStats[0].total_revenue > 0
      ? ((monthlyStats[0].total_revenue - previousMonthStats[0].total_revenue) / previousMonthStats[0].total_revenue) * 100
      : 0;

    const prepTimeChange = previousMonthStats[0].avg_preparation_time > 0
      ? ((monthlyStats[0].avg_preparation_time - previousMonthStats[0].avg_preparation_time) / previousMonthStats[0].avg_preparation_time) * 100
      : 0;

    const response = {
      totalOrders: monthlyStats[0].total_orders,
      orderChange: orderChange.toFixed(1),
      monthlyRevenue: monthlyStats[0].total_revenue,
      revenueChange: revenueChange.toFixed(1),
      popularItems: popularItems,
      avgPreparationTime: Math.round(monthlyStats[0].avg_preparation_time),
      prepTimeChange: prepTimeChange.toFixed(1)
    };

    console.log('Merchant analytics response:', response);
    res.json(response);
  } catch (error) {
    console.error('Error in /api/merchant/analytics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Notification API Routes
app.get('/api/notifications', async (req, res) => {
  try {
    const { userId } = req.query;
    console.log('Fetching notifications for user:', userId);

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Use mock data if database is not available
    if (useMockData) {
      const notifications = mockData.notifications.filter(n => n.user_id === userId);
      console.log('Found notifications:', notifications.length);
      res.json(notifications);
      return;
    }

    const [notifications] = await pool.query(`
      SELECT * FROM notifications
      WHERE user_id = ?
      ORDER BY created_at DESC
    `, [userId]);

    console.log('Found notifications:', notifications.length);
    res.json(notifications || []);
  } catch (error) {
    console.error('Error in /api/notifications:', error);
    res.status(500).json({
      error: 'Failed to fetch notifications',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

app.get('/api/notifications/unread-count', async (req, res) => {
  try {
    const { userId } = req.query;
    console.log('Fetching unread count for user:', userId);

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Use mock data if database is not available
    if (useMockData) {
      const unreadCount = mockData.notifications.filter(n => n.user_id === userId && !n.is_read).length;
      console.log('Unread count:', unreadCount);
      res.json(unreadCount);
      return;
    }

    const [result] = await pool.query(`
      SELECT COUNT(*) as unread_count FROM notifications
      WHERE user_id = ? AND is_read = false
    `, [userId]);

    const unreadCount = result[0].unread_count || 0;
    console.log('Unread count:', unreadCount);
    res.json(unreadCount);
  } catch (error) {
    console.error('Error in /api/notifications/unread-count:', error);
    res.status(500).json({
      error: 'Failed to fetch unread count',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

app.put('/api/notifications/:notificationId/read', async (req, res) => {
  try {
    const { notificationId } = req.params;
    console.log('Marking notification as read:', notificationId);

    // Use mock data if database is not available
    if (useMockData) {
      const notification = mockData.notifications.find(n => n.id === notificationId);
      if (notification) {
        notification.is_read = true;
        res.json({ message: 'Notification marked as read' });
      } else {
        res.status(404).json({ error: 'Notification not found' });
      }
      return;
    }

    await pool.query(`
      UPDATE notifications
      SET is_read = true
      WHERE id = ?
    `, [notificationId]);

    res.json({ message: 'Notification marked as read' });
  } catch (error) {
    console.error('Error in /api/notifications/:notificationId/read:', error);
    res.status(500).json({
      error: 'Failed to mark notification as read',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

app.put('/api/notifications/read-all', async (req, res) => {
  try {
    const { userId } = req.body;
    console.log('Marking all notifications as read for user:', userId);

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Use mock data if database is not available
    if (useMockData) {
      mockData.notifications.forEach(n => {
        if (n.user_id === userId) {
          n.is_read = true;
        }
      });
      res.json({ message: 'All notifications marked as read' });
      return;
    }

    await pool.query(`
      UPDATE notifications
      SET is_read = true
      WHERE user_id = ?
    `, [userId]);

    res.json({ message: 'All notifications marked as read' });
  } catch (error) {
    console.error('Error in /api/notifications/read-all:', error);
    res.status(500).json({
      error: 'Failed to mark all notifications as read',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

app.delete('/api/notifications/:notificationId', async (req, res) => {
  try {
    const { notificationId } = req.params;
    console.log('Deleting notification:', notificationId);

    // Use mock data if database is not available
    if (useMockData) {
      const index = mockData.notifications.findIndex(n => n.id === notificationId);
      if (index !== -1) {
        mockData.notifications.splice(index, 1);
        res.json({ message: 'Notification deleted' });
      } else {
        res.status(404).json({ error: 'Notification not found' });
      }
      return;
    }

    await pool.query(`
      DELETE FROM notifications
      WHERE id = ?
    `, [notificationId]);

    res.json({ message: 'Notification deleted' });
  } catch (error) {
    console.error('Error in /api/notifications/:notificationId:', error);
    res.status(500).json({
      error: 'Failed to delete notification',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Global error handler:', err);
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
});

// 404 handler
app.use((req, res) => {
  console.log('404 Not Found:', req.method, req.url);
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found'
  });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log('Environment:', process.env.NODE_ENV || 'development');
  console.log('Using mock data:', useMockData);
  console.log('Database config:', {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    database: process.env.DB_NAME || 'foodflow_db'
  });
  console.log('Available API endpoints:');
  console.log('- GET /api/merchant/reviews?merchantId=merchant-1');
  console.log('- GET /api/notifications?userId=merchant-1');
  console.log('- GET /api/notifications/unread-count?userId=merchant-1');
  console.log('- GET /api/merchant/menu?merchantId=merchant-1');
  console.log('- GET /api/merchant/orders?merchantId=merchant-1');
  console.log('- GET /api/merchant/categories?merchantId=merchant-1');
  console.log('- GET /api/merchant/operating-hours?merchantId=merchant-1');
});
