const express = require('express');
const cors = require('cors');
const mysql = require('mysql2/promise');
const dotenv = require('dotenv');
const path = require('path');

dotenv.config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, '../public')));

// Basic route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to FoodFlow Connect API' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Mock database for development (when MySQL is not available)
let useMockData = true; // Force mock data for now

// Database connection
const pool = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  port: Number(process.env.DB_PORT) || 3306,
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'foodflow_db',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Test database connection
pool.getConnection()
  .then(connection => {
    console.log('Database connected successfully');
    connection.release();
  })
  .catch(err => {
    console.error('Error connecting to the database:', err);
    console.log('Switching to mock data mode for development...');
    useMockData = true;
  });

// Mock data for development
const mockData = {
  users: [
    {
      id: 'merchant-1',
      name: 'Demo Merchant',
      email: '<EMAIL>',
      role: 'merchant',
      phone: '+62 811 3333 3333',
      status: 'active',
      business_name: 'Warung Nasi Padang'
    },
    {
      id: 'customer-1',
      name: 'Demo Customer',
      email: '<EMAIL>',
      role: 'customer',
      phone: '+62 811 2222 2222',
      status: 'active'
    }
  ],
  transactions: [
    {
      id: 'transaction-1',
      user_id: 'customer-1',
      merchant_id: 'merchant-1',
      total_amount: 50000,
      delivery_fee: 10000,
      status: 'delivered',
      created_at: new Date('2024-01-15T10:30:00Z')
    },
    {
      id: 'transaction-2',
      user_id: 'customer-1',
      merchant_id: 'merchant-1',
      total_amount: 75000,
      delivery_fee: 10000,
      status: 'delivered',
      created_at: new Date('2024-01-10T14:20:00Z')
    }
  ],
  merchant_reviews: [
    {
      id: 'review-1',
      user_id: 'customer-1',
      merchant_id: 'merchant-1',
      transaction_id: 'transaction-1',
      rating: 4.5,
      comment: 'Makanannya enak dan pelayanan cepat',
      food_rating: 4.5,
      service_rating: 4.0,
      delivery_rating: 5.0,
      created_at: new Date('2024-01-15T11:00:00Z'),
      customer_name: 'Demo Customer',
      customer_avatar: null,
      order_date: new Date('2024-01-15T10:30:00Z')
    },
    {
      id: 'review-2',
      user_id: 'customer-1',
      merchant_id: 'merchant-1',
      transaction_id: 'transaction-2',
      rating: 4.8,
      comment: 'Rendangnya mantap, bumbunya pas',
      food_rating: 5.0,
      service_rating: 4.5,
      delivery_rating: 4.5,
      created_at: new Date('2024-01-10T15:00:00Z'),
      customer_name: 'Demo Customer',
      customer_avatar: null,
      order_date: new Date('2024-01-10T14:20:00Z')
    },
    {
      id: 'review-3',
      user_id: 'customer-1',
      merchant_id: 'merchant-1',
      transaction_id: null,
      rating: 4.2,
      comment: 'Pengiriman cepat, makanan masih hangat',
      food_rating: 4.0,
      service_rating: 4.5,
      delivery_rating: 4.0,
      created_at: new Date('2024-01-05T12:30:00Z'),
      customer_name: 'Demo Customer',
      customer_avatar: null,
      order_date: null
    }
  ],
  notifications: [
    {
      id: 'notif-1',
      user_id: 'merchant-1',
      title: 'Pesanan Baru',
      message: 'Anda mendapat pesanan baru dari Demo Customer',
      type: 'order',
      is_read: false,
      created_at: new Date('2024-01-15T10:30:00Z')
    },
    {
      id: 'notif-2',
      user_id: 'merchant-1',
      title: 'Review Baru',
      message: 'Demo Customer memberikan review untuk pesanan Anda',
      type: 'system',
      is_read: false,
      created_at: new Date('2024-01-15T11:00:00Z')
    },
    {
      id: 'notif-3',
      user_id: 'merchant-1',
      title: 'Promosi Berakhir',
      message: 'Promosi diskon 20% akan berakhir dalam 2 hari',
      type: 'promotion',
      is_read: true,
      created_at: new Date('2024-01-14T08:00:00Z')
    }
  ],
  menu_items: [
    {
      id: 'menu-1',
      merchant_id: 'merchant-1',
      name: 'Rendang Daging',
      price: 25000,
      description: 'Rendang daging sapi dengan bumbu tradisional',
      image: null,
      is_available: true,
      created_at: new Date('2024-01-01T08:00:00Z')
    },
    {
      id: 'menu-2',
      merchant_id: 'merchant-1',
      name: 'Ayam Gulai',
      price: 20000,
      description: 'Ayam gulai dengan santan kelapa',
      image: null,
      is_available: true,
      created_at: new Date('2024-01-01T08:00:00Z')
    }
  ],
  menu_categories: [
    {
      id: 'cat-1',
      merchant_id: 'merchant-1',
      name: 'Makanan Utama',
      description: 'Menu makanan utama',
      image: null,
      is_active: true,
      created_at: new Date('2024-01-01T08:00:00Z')
    }
  ],
  operating_hours: [
    {
      id: 'hours-1',
      merchant_id: 'merchant-1',
      day_of_week: 'monday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-2',
      merchant_id: 'merchant-1',
      day_of_week: 'tuesday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-3',
      merchant_id: 'merchant-1',
      day_of_week: 'wednesday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-4',
      merchant_id: 'merchant-1',
      day_of_week: 'thursday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-5',
      merchant_id: 'merchant-1',
      day_of_week: 'friday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-6',
      merchant_id: 'merchant-1',
      day_of_week: 'saturday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    },
    {
      id: 'hours-7',
      merchant_id: 'merchant-1',
      day_of_week: 'sunday',
      open_time: '08:00:00',
      close_time: '22:00:00',
      is_closed: false
    }
  ]
};

// API Routes
app.get('/api/admin/stats', async (req, res) => {
  try {
    console.log('Fetching dashboard stats...');

    const [users] = await pool.query(`
      SELECT
        COUNT(*) as total_users,
        SUM(CASE WHEN role = 'merchant' THEN 1 ELSE 0 END) as total_merchants,
        SUM(CASE WHEN role = 'driver' THEN 1 ELSE 0 END) as total_drivers
      FROM users
      WHERE status = 'active'
    `);
    console.log('Users query result:', users[0]);

    const [revenue] = await pool.query(`
      SELECT
        COUNT(*) as total_transactions,
        COALESCE(SUM(total_amount), 0) as total_revenue
      FROM transactions
      WHERE status = 'delivered'
      AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
    `);
    console.log('Revenue query result:', revenue[0]);

    const response = {
      totalUsers: users[0].total_users || 0,
      totalMerchants: users[0].total_merchants || 0,
      totalDrivers: users[0].total_drivers || 0,
      totalTransactions: revenue[0].total_transactions || 0,
      totalRevenue: revenue[0].total_revenue || 0
    };
    console.log('Sending response:', response);

    res.json(response);
  } catch (error) {
    console.error('Error in /api/admin/stats:', error);
    res.status(500).json({
      error: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

app.get('/api/admin/users', async (req, res) => {
  try {
    const { search, role } = req.query;
    let query = `
      SELECT * FROM users
      WHERE 1=1
    `;
    const params = [];

    if (search) {
      query += ` AND (name LIKE ? OR email LIKE ?)`;
      params.push(`%${search}%`, `%${search}%`);
    }

    if (role && role !== 'all') {
      query += ` AND role = ?`;
      params.push(role);
    }

    query += ` ORDER BY created_at DESC`;

    const [users] = await pool.query(query, params);
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/admin/pending-users', async (req, res) => {
  try {
    const [users] = await pool.query(`
      SELECT * FROM users
      WHERE status = 'pending'
      ORDER BY created_at DESC
    `);
    res.json(users);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/admin/approve-user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    await pool.query(`
      UPDATE users
      SET status = 'active'
      WHERE id = ?
    `, [userId]);

    await pool.query(`
      INSERT INTO activity_logs (id, user_id, action, description)
      VALUES (UUID(), ?, 'USER_APPROVED', 'User approved by admin')
    `, [userId]);

    res.json({ message: 'User approved successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/admin/reject-user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    await pool.query(`
      UPDATE users
      SET status = 'suspended'
      WHERE id = ?
    `, [userId]);

    await pool.query(`
      INSERT INTO activity_logs (id, user_id, action, description)
      VALUES (UUID(), ?, 'USER_REJECTED', 'User rejected by admin')
    `, [userId]);

    res.json({ message: 'User rejected successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/admin/suspend-user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    await pool.query(`
      UPDATE users
      SET status = 'suspended'
      WHERE id = ?
    `, [userId]);

    await pool.query(`
      INSERT INTO activity_logs (id, user_id, action, description)
      VALUES (UUID(), ?, 'USER_SUSPENDED', 'User suspended by admin')
    `, [userId]);

    res.json({ message: 'User suspended successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Driver API Routes
app.get('/api/driver/orders', async (req, res) => {
  try {
    const [orders] = await pool.query(`
      SELECT
        t.*,
        u.name as customer_name,
        u.phone as customer_phone,
        m.name as merchant_name,
        m.phone as merchant_phone,
        m.business_name
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users m ON t.merchant_id = m.id
      WHERE t.driver_id IS NULL
      AND t.status = 'pending'
      ORDER BY t.created_at DESC
    `);
    res.json(orders);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/driver/active-orders', async (req, res) => {
  try {
    const { driverId } = req.query;
    const [orders] = await pool.query(`
      SELECT
        t.*,
        u.name as customer_name,
        u.phone as customer_phone,
        m.name as merchant_name,
        m.phone as merchant_phone,
        m.business_name
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users m ON t.merchant_id = m.id
      WHERE t.driver_id = ?
      AND t.status IN ('processing', 'delivering')
      ORDER BY t.created_at DESC
    `, [driverId]);
    res.json(orders);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/driver/accept-order', async (req, res) => {
  try {
    const { orderId, driverId } = req.body;
    await pool.query(`
      UPDATE transactions
      SET driver_id = ?, status = 'processing'
      WHERE id = ? AND driver_id IS NULL
    `, [driverId, orderId]);

    await pool.query(`
      INSERT INTO activity_logs (id, user_id, action, description)
      VALUES (UUID(), ?, 'ORDER_ACCEPTED', 'Driver accepted order')
    `, [driverId]);

    res.json({ message: 'Order accepted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/driver/update-order-status', async (req, res) => {
  try {
    const { orderId, status } = req.body;
    await pool.query(`
      UPDATE transactions
      SET status = ?
      WHERE id = ?
    `, [status, orderId]);

    res.json({ message: 'Order status updated successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Merchant API Routes
app.get('/api/merchant/menu', async (req, res) => {
  try {
    const { merchantId } = req.query;

    // Use mock data if database is not available
    if (useMockData) {
      const menu = mockData.menu_items.filter(item => item.merchant_id === merchantId);
      res.json(menu);
      return;
    }

    const [menu] = await pool.query(`
      SELECT * FROM menu_items
      WHERE merchant_id = ?
      ORDER BY created_at DESC
    `, [merchantId]);
    res.json(menu);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/merchant/menu', async (req, res) => {
  try {
    const { merchantId, name, price, description, image } = req.body;
    const [result] = await pool.query(`
      INSERT INTO menu_items (id, merchant_id, name, price, description, image)
      VALUES (UUID(), ?, ?, ?, ?, ?)
    `, [merchantId, name, price, description, image]);
    res.json({ id: result.insertId, message: 'Menu item added successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/merchant/menu/:itemId', async (req, res) => {
  try {
    const { itemId } = req.params;
    const { name, price, description, image, is_available } = req.body;
    await pool.query(`
      UPDATE menu_items
      SET name = ?, price = ?, description = ?, image = ?, is_available = ?
      WHERE id = ?
    `, [name, price, description, image, is_available, itemId]);
    res.json({ message: 'Menu item updated successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/merchant/orders', async (req, res) => {
  try {
    const { merchantId } = req.query;

    // Use mock data if database is not available
    if (useMockData) {
      const orders = mockData.transactions
        .filter(t => t.merchant_id === merchantId)
        .map(t => ({
          ...t,
          customer_name: 'Demo Customer',
          customer_phone: '+62 811 2222 2222',
          driver_name: null,
          driver_phone: null
        }));
      res.json(orders);
      return;
    }

    const [orders] = await pool.query(`
      SELECT
        t.*,
        u.name as customer_name,
        u.phone as customer_phone,
        d.name as driver_name,
        d.phone as driver_phone
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users d ON t.driver_id = d.id
      WHERE t.merchant_id = ?
      ORDER BY t.created_at DESC
    `, [merchantId]);
    res.json(orders);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Menu Categories
app.get('/api/merchant/categories', async (req, res) => {
  try {
    const { merchantId } = req.query;

    // Use mock data if database is not available
    if (useMockData) {
      const categories = mockData.menu_categories.filter(cat => cat.merchant_id === merchantId);
      res.json(categories);
      return;
    }

    const [categories] = await pool.query(`
      SELECT * FROM menu_categories
      WHERE merchant_id = ?
      ORDER BY name ASC
    `, [merchantId]);
    res.json(categories);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/merchant/categories', async (req, res) => {
  try {
    const { merchantId, name, description, image } = req.body;
    const [result] = await pool.query(`
      INSERT INTO menu_categories (id, merchant_id, name, description, image)
      VALUES (UUID(), ?, ?, ?, ?)
    `, [merchantId, name, description, image]);
    res.json({ id: result.insertId, message: 'Category added successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/merchant/categories/:categoryId', async (req, res) => {
  try {
    const { categoryId } = req.params;
    const { name, description, image, is_active } = req.body;
    await pool.query(`
      UPDATE menu_categories
      SET name = ?, description = ?, image = ?, is_active = ?
      WHERE id = ?
    `, [name, description, image, is_active, categoryId]);
    res.json({ message: 'Category updated successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Operating Hours
app.get('/api/merchant/operating-hours', async (req, res) => {
  try {
    const { merchantId } = req.query;

    // Use mock data if database is not available
    if (useMockData) {
      const hours = mockData.operating_hours.filter(h => h.merchant_id === merchantId);
      res.json(hours);
      return;
    }

    const [hours] = await pool.query(`
      SELECT * FROM operating_hours
      WHERE merchant_id = ?
      ORDER BY day_of_week ASC
    `, [merchantId]);
    res.json(hours);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.put('/api/merchant/operating-hours', async (req, res) => {
  try {
    const { merchantId, hours } = req.body;
    const connection = await pool.getConnection();
    await connection.beginTransaction();

    try {
      // Delete existing hours
      await connection.query(`
        DELETE FROM operating_hours
        WHERE merchant_id = ?
      `, [merchantId]);

      // Insert new hours
      for (const hour of hours) {
        await connection.query(`
          INSERT INTO operating_hours (
            id, merchant_id, day_of_week, open_time, close_time, is_closed
          ) VALUES (UUID(), ?, ?, ?, ?, ?)
        `, [merchantId, hour.day_of_week, hour.open_time, hour.close_time, hour.is_closed]);
      }

      await connection.commit();
      res.json({ message: 'Operating hours updated successfully' });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Reviews
app.get('/api/merchant/reviews', async (req, res) => {
  try {
    const { merchantId } = req.query;
    console.log('Fetching reviews for merchant:', merchantId);

    if (!merchantId) {
      return res.status(400).json({ error: 'Merchant ID is required' });
    }

    // Use mock data if database is not available
    if (useMockData) {
      console.log('Using mock data for reviews');

      // Check if merchant exists in mock data
      const merchant = mockData.users.find(u => u.id === merchantId && u.role === 'merchant');
      if (!merchant) {
        return res.status(404).json({ error: 'Merchant not found' });
      }

      // Filter reviews for this merchant
      const reviews = mockData.merchant_reviews.filter(r => r.merchant_id === merchantId);
      console.log('Found reviews:', reviews.length);
      res.json(reviews);
      return;
    }

    // First check if merchant exists
    const [merchant] = await pool.query(
      'SELECT id FROM users WHERE id = ? AND role = ?',
      [merchantId, 'merchant']
    );

    if (!merchant.length) {
      return res.status(404).json({ error: 'Merchant not found' });
    }

    const [reviews] = await pool.query(`
      SELECT
        mr.*,
        u.name as customer_name,
        u.avatar as customer_avatar,
        t.id as transaction_id,
        t.created_at as order_date
      FROM merchant_reviews mr
      LEFT JOIN users u ON mr.user_id = u.id
      LEFT JOIN transactions t ON mr.transaction_id = t.id
      WHERE mr.merchant_id = ?
      ORDER BY mr.created_at DESC
    `, [merchantId]);

    console.log('Found reviews:', reviews.length);
    res.json(reviews || []);
  } catch (error) {
    console.error('Error in /api/merchant/reviews:', error);
    console.error('Error details:', error.message, error.code, error.sqlMessage);
    res.status(500).json({
      error: 'Failed to fetch merchant reviews',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Customer API Routes
app.get('/api/customer/restaurants', async (req, res) => {
  try {
    const [restaurants] = await pool.query(`
      SELECT
        u.*,
        COUNT(DISTINCT t.id) as total_orders,
        AVG(t.rating) as average_rating
      FROM users u
      LEFT JOIN transactions t ON u.id = t.merchant_id
      WHERE u.role = 'merchant'
      AND u.status = 'active'
      GROUP BY u.id
      ORDER BY average_rating DESC
    `);
    res.json(restaurants);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/customer/restaurant/:merchantId/menu', async (req, res) => {
  try {
    const { merchantId } = req.params;
    const [menu] = await pool.query(`
      SELECT * FROM menu_items
      WHERE merchant_id = ?
      AND is_available = true
      ORDER BY created_at DESC
    `, [merchantId]);
    res.json(menu);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/customer/orders', async (req, res) => {
  try {
    const { userId, merchantId, items, totalAmount, deliveryFee, deliveryAddress } = req.body;

    // Start transaction
    const connection = await pool.getConnection();
    await connection.beginTransaction();

    try {
      // Create order
      const [orderResult] = await connection.query(`
        INSERT INTO transactions (
          id, user_id, merchant_id, total_amount, delivery_fee,
          delivery_address, status
        ) VALUES (UUID(), ?, ?, ?, ?, ?, 'pending')
      `, [userId, merchantId, totalAmount, deliveryFee, deliveryAddress]);

      const orderId = orderResult.insertId;

      // Add order items
      for (const item of items) {
        await connection.query(`
          INSERT INTO transaction_items (
            id, transaction_id, menu_name, quantity, price
          ) VALUES (UUID(), ?, ?, ?, ?)
        `, [orderId, item.name, item.quantity, item.price]);
      }

      await connection.commit();
      res.json({ orderId, message: 'Order created successfully' });
    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/customer/orders', async (req, res) => {
  try {
    const { userId } = req.query;
    const [orders] = await pool.query(`
      SELECT
        t.*,
        m.name as merchant_name,
        m.business_name,
        d.name as driver_name,
        d.phone as driver_phone
      FROM transactions t
      LEFT JOIN users m ON t.merchant_id = m.id
      LEFT JOIN users d ON t.driver_id = d.id
      WHERE t.user_id = ?
      ORDER BY t.created_at DESC
    `, [userId]);
    res.json(orders);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Admin API Routes
app.get('/api/admin/transactions', async (req, res) => {
  try {
    console.log('Fetching recent transactions...');
    const [transactions] = await pool.query(`
      SELECT
        t.*,
        u.name as customer_name,
        m.name as merchant_name,
        d.name as driver_name
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users m ON t.merchant_id = m.id
      LEFT JOIN users d ON t.driver_id = d.id
      ORDER BY t.created_at DESC
      LIMIT 10
    `);
    console.log('Transactions query result:', transactions);
    res.json(transactions);
  } catch (error) {
    console.error('Error in /api/admin/transactions:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/admin/activity-logs', async (req, res) => {
  try {
    console.log('Fetching activity logs...');
    const [logs] = await pool.query(`
      SELECT
        al.*,
        u.name as user_name,
        u.role as user_role
      FROM activity_logs al
      LEFT JOIN users u ON al.user_id = u.id
      ORDER BY al.created_at DESC
      LIMIT 10
    `);
    console.log('Activity logs query result:', logs);
    res.json(logs);
  } catch (error) {
    console.error('Error in /api/admin/activity-logs:', error);
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/admin/system-settings', async (req, res) => {
  try {
    console.log('Fetching system settings...');
    const [settings] = await pool.query(`
      SELECT * FROM system_settings
      ORDER BY id DESC
      LIMIT 1
    `);
    console.log('System settings query result:', settings[0]);
    res.json(settings[0] || {
      price_markup_percentage: 0,
      delivery_base_rate: 0,
      merchant_commission_percentage: 0,
      driver_commission_percentage: 0
    });
  } catch (error) {
    console.error('Error in /api/admin/system-settings:', error);
    res.status(500).json({ error: error.message });
  }
});

// Merchant Analytics Routes
app.get('/api/merchant/analytics', async (req, res) => {
  try {
    const { merchantId } = req.query;
    console.log('Fetching merchant analytics for:', merchantId);

    // Get total orders and revenue for last 30 days
    const [monthlyStats] = await pool.query(`
      SELECT
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(AVG(TIMESTAMPDIFF(MINUTE, created_at, updated_at)), 0) as avg_preparation_time
      FROM transactions
      WHERE merchant_id = ?
      AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      AND status = 'delivered'
    `, [merchantId]);

    // Get previous month stats for comparison
    const [previousMonthStats] = await pool.query(`
      SELECT
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(AVG(TIMESTAMPDIFF(MINUTE, created_at, updated_at)), 0) as avg_preparation_time
      FROM transactions
      WHERE merchant_id = ?
      AND created_at >= DATE_SUB(NOW(), INTERVAL 60 DAY)
      AND created_at < DATE_SUB(NOW(), INTERVAL 30 DAY)
      AND status = 'delivered'
    `, [merchantId]);

    // Get most popular menu items
    const [popularItems] = await pool.query(`
      SELECT
        mi.name,
        COUNT(ti.id) as order_count
      FROM transaction_items ti
      JOIN transactions t ON ti.transaction_id = t.id
      JOIN menu_items mi ON ti.menu_name = mi.name
      WHERE t.merchant_id = ?
      AND t.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
      GROUP BY mi.name
      ORDER BY order_count DESC
      LIMIT 5
    `, [merchantId]);

    // Calculate percentage changes
    const orderChange = previousMonthStats[0].total_orders > 0
      ? ((monthlyStats[0].total_orders - previousMonthStats[0].total_orders) / previousMonthStats[0].total_orders) * 100
      : 0;

    const revenueChange = previousMonthStats[0].total_revenue > 0
      ? ((monthlyStats[0].total_revenue - previousMonthStats[0].total_revenue) / previousMonthStats[0].total_revenue) * 100
      : 0;

    const prepTimeChange = previousMonthStats[0].avg_preparation_time > 0
      ? ((monthlyStats[0].avg_preparation_time - previousMonthStats[0].avg_preparation_time) / previousMonthStats[0].avg_preparation_time) * 100
      : 0;

    const response = {
      totalOrders: monthlyStats[0].total_orders,
      orderChange: orderChange.toFixed(1),
      monthlyRevenue: monthlyStats[0].total_revenue,
      revenueChange: revenueChange.toFixed(1),
      popularItems: popularItems,
      avgPreparationTime: Math.round(monthlyStats[0].avg_preparation_time),
      prepTimeChange: prepTimeChange.toFixed(1)
    };

    console.log('Merchant analytics response:', response);
    res.json(response);
  } catch (error) {
    console.error('Error in /api/merchant/analytics:', error);
    res.status(500).json({ error: error.message });
  }
});

// Notification API Routes
app.get('/api/notifications', async (req, res) => {
  try {
    const { userId } = req.query;
    console.log('Fetching notifications for user:', userId);

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Use mock data if database is not available
    if (useMockData) {
      const notifications = mockData.notifications.filter(n => n.user_id === userId);
      console.log('Found notifications:', notifications.length);
      res.json(notifications);
      return;
    }

    const [notifications] = await pool.query(`
      SELECT * FROM notifications
      WHERE user_id = ?
      ORDER BY created_at DESC
    `, [userId]);

    console.log('Found notifications:', notifications.length);
    res.json(notifications || []);
  } catch (error) {
    console.error('Error in /api/notifications:', error);
    res.status(500).json({
      error: 'Failed to fetch notifications',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

app.get('/api/notifications/unread-count', async (req, res) => {
  try {
    const { userId } = req.query;
    console.log('Fetching unread count for user:', userId);

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Use mock data if database is not available
    if (useMockData) {
      const unreadCount = mockData.notifications.filter(n => n.user_id === userId && !n.is_read).length;
      console.log('Unread count:', unreadCount);
      res.json(unreadCount);
      return;
    }

    const [result] = await pool.query(`
      SELECT COUNT(*) as unread_count FROM notifications
      WHERE user_id = ? AND is_read = false
    `, [userId]);

    const unreadCount = result[0].unread_count || 0;
    console.log('Unread count:', unreadCount);
    res.json(unreadCount);
  } catch (error) {
    console.error('Error in /api/notifications/unread-count:', error);
    res.status(500).json({
      error: 'Failed to fetch unread count',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

app.put('/api/notifications/:notificationId/read', async (req, res) => {
  try {
    const { notificationId } = req.params;
    console.log('Marking notification as read:', notificationId);

    // Use mock data if database is not available
    if (useMockData) {
      const notification = mockData.notifications.find(n => n.id === notificationId);
      if (notification) {
        notification.is_read = true;
        res.json({ message: 'Notification marked as read' });
      } else {
        res.status(404).json({ error: 'Notification not found' });
      }
      return;
    }

    await pool.query(`
      UPDATE notifications
      SET is_read = true
      WHERE id = ?
    `, [notificationId]);

    res.json({ message: 'Notification marked as read' });
  } catch (error) {
    console.error('Error in /api/notifications/:notificationId/read:', error);
    res.status(500).json({
      error: 'Failed to mark notification as read',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

app.put('/api/notifications/read-all', async (req, res) => {
  try {
    const { userId } = req.body;
    console.log('Marking all notifications as read for user:', userId);

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Use mock data if database is not available
    if (useMockData) {
      mockData.notifications.forEach(n => {
        if (n.user_id === userId) {
          n.is_read = true;
        }
      });
      res.json({ message: 'All notifications marked as read' });
      return;
    }

    await pool.query(`
      UPDATE notifications
      SET is_read = true
      WHERE user_id = ?
    `, [userId]);

    res.json({ message: 'All notifications marked as read' });
  } catch (error) {
    console.error('Error in /api/notifications/read-all:', error);
    res.status(500).json({
      error: 'Failed to mark all notifications as read',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

app.delete('/api/notifications/:notificationId', async (req, res) => {
  try {
    const { notificationId } = req.params;
    console.log('Deleting notification:', notificationId);

    // Use mock data if database is not available
    if (useMockData) {
      const index = mockData.notifications.findIndex(n => n.id === notificationId);
      if (index !== -1) {
        mockData.notifications.splice(index, 1);
        res.json({ message: 'Notification deleted' });
      } else {
        res.status(404).json({ error: 'Notification not found' });
      }
      return;
    }

    await pool.query(`
      DELETE FROM notifications
      WHERE id = ?
    `, [notificationId]);

    res.json({ message: 'Notification deleted' });
  } catch (error) {
    console.error('Error in /api/notifications/:notificationId:', error);
    res.status(500).json({
      error: 'Failed to delete notification',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Global error handler:', err);
  res.status(500).json({
    error: 'Something went wrong!',
    message: process.env.NODE_ENV === 'development' ? err.message : undefined,
    stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
  });
});

// 404 handler
app.use((req, res) => {
  console.log('404 Not Found:', req.method, req.url);
  res.status(404).json({
    error: 'Not Found',
    message: 'The requested resource was not found'
  });
});

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log('Environment:', process.env.NODE_ENV || 'development');
  console.log('Using mock data:', useMockData);
  console.log('Database config:', {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    database: process.env.DB_NAME || 'foodflow_db'
  });
  console.log('Available API endpoints:');
  console.log('- GET /api/merchant/reviews?merchantId=merchant-1');
  console.log('- GET /api/notifications?userId=merchant-1');
  console.log('- GET /api/notifications/unread-count?userId=merchant-1');
  console.log('- GET /api/merchant/menu?merchantId=merchant-1');
  console.log('- GET /api/merchant/orders?merchantId=merchant-1');
  console.log('- GET /api/merchant/categories?merchantId=merchant-1');
  console.log('- GET /api/merchant/operating-hours?merchantId=merchant-1');
});
