import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Download, Printer } from 'lucide-react';

interface InvoiceData {
  id: string;
  invoice_number: string;
  user_name: string;
  user_role: 'merchant' | 'driver';
  business_name?: string;
  user_email?: string;
  user_phone?: string;
  total_earnings: number;
  total_transactions: number;
  commission_rate: number;
  commission_amount: number;
  net_payment: number;
  period_start: string;
  period_end: string;
  payment_date?: string;
  transactions?: Array<{
    id: string;
    date: string;
    amount: number;
    commission: number;
    description: string;
  }>;
}

interface InvoiceGeneratorProps {
  invoiceData: InvoiceData;
  onDownload: () => void;
  onPrint: () => void;
}

const InvoiceGenerator: React.FC<InvoiceGeneratorProps> = ({ 
  invoiceData, 
  onDownload, 
  onPrint 
}) => {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white">
      {/* Header Actions */}
      <div className="flex justify-end gap-2 mb-6 no-print">
        <Button onClick={onPrint} variant="outline" className="flex items-center gap-2">
          <Printer className="h-4 w-4" />
          Print
        </Button>
        <Button onClick={onDownload} className="flex items-center gap-2">
          <Download className="h-4 w-4" />
          Download PDF
        </Button>
      </div>

      {/* Invoice Content */}
      <Card className="invoice-content">
        <CardContent className="p-8">
          {/* Header */}
          <div className="flex justify-between items-start mb-8">
            <div>
              <h1 className="text-3xl font-bold text-orange-600 mb-2">FoodFlow</h1>
              <p className="text-gray-600">Platform Delivery Makanan</p>
              <div className="mt-4 text-sm text-gray-600">
                <p>Jl. Sudirman No. 123</p>
                <p>Jakarta Pusat, 10220</p>
                <p>Indonesia</p>
                <p>Email: <EMAIL></p>
                <p>Phone: +62 21 1234 5678</p>
              </div>
            </div>
            <div className="text-right">
              <h2 className="text-2xl font-bold mb-2">INVOICE</h2>
              <p className="text-lg font-semibold text-orange-600">{invoiceData.invoice_number}</p>
              <div className="mt-4 text-sm">
                <p><strong>Tanggal Invoice:</strong> {formatDate(new Date().toISOString())}</p>
                {invoiceData.payment_date && (
                  <p><strong>Tanggal Bayar:</strong> {formatDate(invoiceData.payment_date)}</p>
                )}
              </div>
            </div>
          </div>

          {/* Bill To */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">Tagihan Untuk:</h3>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="font-semibold text-lg">{invoiceData.user_name}</p>
              {invoiceData.business_name && (
                <p className="text-gray-600">{invoiceData.business_name}</p>
              )}
              <div className="mt-2">
                <Badge variant="outline" className="capitalize">
                  {invoiceData.user_role === 'merchant' ? 'Merchant Partner' : 'Driver Partner'}
                </Badge>
              </div>
              {invoiceData.user_email && (
                <p className="text-sm text-gray-600 mt-2">Email: {invoiceData.user_email}</p>
              )}
              {invoiceData.user_phone && (
                <p className="text-sm text-gray-600">Phone: {invoiceData.user_phone}</p>
              )}
            </div>
          </div>

          {/* Period */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-2">Periode Pembayaran:</h3>
            <p className="text-gray-700">
              {formatDate(invoiceData.period_start)} - {formatDate(invoiceData.period_end)}
            </p>
          </div>

          {/* Summary Table */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold mb-4">Ringkasan Pembayaran:</h3>
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-left p-4 font-semibold">Deskripsi</th>
                    <th className="text-right p-4 font-semibold">Jumlah</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-t">
                    <td className="p-4">Total Transaksi</td>
                    <td className="p-4 text-right">{invoiceData.total_transactions}</td>
                  </tr>
                  <tr className="border-t">
                    <td className="p-4">Total Pendapatan Kotor</td>
                    <td className="p-4 text-right font-semibold">{formatCurrency(invoiceData.total_earnings)}</td>
                  </tr>
                  <tr className="border-t">
                    <td className="p-4">
                      Komisi Platform ({invoiceData.commission_rate}%)
                      <span className="text-sm text-gray-500 block">
                        {invoiceData.user_role === 'merchant' ? 'Biaya layanan platform' : 'Biaya operasional'}
                      </span>
                    </td>
                    <td className="p-4 text-right text-red-600 font-semibold">
                      -{formatCurrency(invoiceData.commission_amount)}
                    </td>
                  </tr>
                  <tr className="border-t bg-green-50">
                    <td className="p-4 font-bold text-lg">Pembayaran Bersih</td>
                    <td className="p-4 text-right font-bold text-lg text-green-600">
                      {formatCurrency(invoiceData.net_payment)}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Transaction Details (if available) */}
          {invoiceData.transactions && invoiceData.transactions.length > 0 && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold mb-4">Detail Transaksi:</h3>
              <div className="border rounded-lg overflow-hidden">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="text-left p-3 font-semibold">Tanggal</th>
                      <th className="text-left p-3 font-semibold">Deskripsi</th>
                      <th className="text-right p-3 font-semibold">Pendapatan</th>
                      <th className="text-right p-3 font-semibold">Komisi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {invoiceData.transactions.map((transaction, index) => (
                      <tr key={transaction.id} className={index % 2 === 0 ? 'bg-gray-25' : ''}>
                        <td className="p-3">{formatDate(transaction.date)}</td>
                        <td className="p-3">{transaction.description}</td>
                        <td className="p-3 text-right">{formatCurrency(transaction.amount)}</td>
                        <td className="p-3 text-right text-red-600">{formatCurrency(transaction.commission)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {/* Payment Instructions */}
          <div className="mb-8 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-2 text-blue-800">Informasi Pembayaran:</h3>
            <div className="text-sm text-blue-700">
              <p className="mb-2">
                Pembayaran akan ditransfer ke rekening yang telah terdaftar dalam sistem.
              </p>
              <p className="mb-2">
                <strong>Jadwal Pembayaran:</strong> Setiap tanggal 1 dan 15 setiap bulan
              </p>
              <p>
                <strong>Metode Pembayaran:</strong> Transfer Bank
              </p>
            </div>
          </div>

          {/* Terms */}
          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold mb-4">Syarat dan Ketentuan:</h3>
            <div className="text-sm text-gray-600 space-y-2">
              <p>1. Invoice ini adalah bukti resmi pembayaran dari FoodFlow kepada mitra.</p>
              <p>2. Pembayaran akan diproses dalam 3-5 hari kerja setelah periode berakhir.</p>
              <p>3. Komisi platform sudah termasuk pajak yang berlaku.</p>
              <p>4. Untuk pertanyaan terkait pembayaran, hubungi tim <NAME_EMAIL></p>
              <p>5. Invoice ini dibuat secara otomatis oleh sistem FoodFlow.</p>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-8 pt-6 border-t text-center text-sm text-gray-500">
            <p>Terima kasih atas kerjasama Anda dengan FoodFlow</p>
            <p className="mt-2">
              Invoice ini dibuat pada {formatDate(new Date().toISOString())} | 
              FoodFlow Indonesia © 2024
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          .no-print {
            display: none !important;
          }
          .invoice-content {
            box-shadow: none !important;
            border: none !important;
          }
          body {
            print-color-adjust: exact;
            -webkit-print-color-adjust: exact;
          }
        }
      `}</style>
    </div>
  );
};

export default InvoiceGenerator;
