import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft, Plus, Clock, CheckCircle, Edit, Trash2, Eye, EyeOff, Tag, Image as ImageIcon } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import EditMenuDialog from './EditMenuDialog';
import DiscountDialog from './DiscountDialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { merchantService } from '../../services/merchantService';
import { notificationService } from '../../services/notificationService';
import { MenuCategoryDialog } from './MenuCategoryDialog';
import { OperatingHoursDialog } from './OperatingHoursDialog';
import { MenuItemDialog } from './MenuItemDialog';
import { OrderList } from './OrderList';
import { ReviewList } from './ReviewList';
import { toast } from 'sonner';

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: string;
  category: string;
  preparationTime: number;
  available: boolean;
  image?: string;
}

const MerchantDashboard = () => {
  const { logout } = useAuth();
  const { toast: useToastToast } = useToast();
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedMenuItem, setSelectedMenuItem] = useState<MenuItem | null>(null);
  const [showDiscountDialog, setShowDiscountDialog] = useState(false);
  const [selectedDiscount, setSelectedDiscount] = useState<any>(null);
  const [discounts, setDiscounts] = useState([
    {
      id: '1',
      title: 'Diskon 20% Nasi Padang',
      description: 'Diskon khusus untuk menu Nasi Padang',
      type: 'percentage',
      value: 20,
      minOrder: 50000,
      maxDiscount: 15000,
      startDate: new Date(),
      endDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      isActive: true,
      usageLimit: 100
    }
  ]);

  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [categories, setCategories] = useState([]);
  const [operatingHours, setOperatingHours] = useState([]);
  const [orders, setOrders] = useState([]);
  const [reviews, setReviews] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [analytics, setAnalytics] = useState({
    totalOrders: 0,
    orderChange: '+0%',
    monthlyRevenue: 0,
    revenueChange: '+0%',
    popularItems: [],
    avgPreparationTime: 0,
    prepTimeChange: '+0%'
  });
  const [merchantInfo, setMerchantInfo] = useState({
    name: '',
    business_name: '',
    business_address: '',
    phone: '',
    email: ''
  });
  const [isCategoryDialogOpen, setIsCategoryDialogOpen] = useState(false);
  const [isOperatingHoursDialogOpen, setIsOperatingHoursDialogOpen] = useState(false);
  const [isMenuItemDialogOpen, setIsMenuItemDialogOpen] = useState(false);
  const [isEditingInfo, setIsEditingInfo] = useState(false);

  const { user } = useAuth();
  const merchantId = user?.id;

  useEffect(() => {
    if (!user) {
      toast.error('Please log in to access the merchant dashboard');
      return;
    }
    if (merchantId) {
      loadDashboardData();
    }
  }, [merchantId, user]);

  const loadDashboardData = async () => {
    try {
      const [menuData, categoryData, hoursData, orderData, reviewData, analyticsData] = await Promise.all([
        merchantService.getMenuItems(merchantId),
        merchantService.getMenuCategories(merchantId),
        merchantService.getOperatingHours(merchantId),
        merchantService.getOrders(merchantId),
        merchantService.getMerchantReviews(merchantId),
        fetch(`http://localhost:5000/api/merchant/analytics?merchantId=${merchantId}`).then(res => res.json())
      ]);

      setMenuItems(menuData);
      setCategories(categoryData);
      setOperatingHours(hoursData);
      setOrders(orderData);
      setReviews(reviewData);
      setAnalytics(analyticsData);

      // Set merchant info from user data
      if (user) {
        setMerchantInfo({
          name: user.name || '',
          business_name: user.business_name || 'Warung Padang Sederhana',
          business_address: user.business_address || 'Jl. Sudirman No. 123, Jakarta Pusat',
          phone: user.phone || '',
          email: user.email || ''
        });
      }

      // Load notifications
      const notificationData = await notificationService.getNotifications(merchantId);
      setNotifications(notificationData);
      const count = await notificationService.getUnreadCount(merchantId);
      setUnreadCount(count);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      toast.error('Failed to load dashboard data. Please try again.');
    }
  };

  const handleAddCategory = async (data) => {
    try {
      await merchantService.addMenuCategory({ ...data, merchantId });
      toast.success('Category added successfully');
      loadDashboardData();
    } catch (error) {
      toast.error('Failed to add category');
    }
  };

  const handleUpdateOperatingHours = async (data) => {
    try {
      await merchantService.updateOperatingHours(merchantId, data);
      toast.success('Operating hours updated successfully');
      loadDashboardData();
    } catch (error) {
      toast.error('Failed to update operating hours');
    }
  };

  const handleAddMenuItem = async (data) => {
    try {
      await merchantService.addMenuItem({ ...data, merchantId });
      toast.success('Menu item added successfully');
      loadDashboardData();
    } catch (error) {
      toast.error('Failed to add menu item');
    }
  };

  const handleAddMenuItemDialog = () => {
    setSelectedMenuItem(null);
    setIsMenuItemDialogOpen(true);
  };

  const handleEditMenuItem = (item: MenuItem) => {
    setSelectedMenuItem(item);
    setEditDialogOpen(true);
  };

  const handleSaveMenuItem = (menuItemData: MenuItem) => {
    if (selectedMenuItem) {
      // Update existing item
      setMenuItems(prev => prev.map(item =>
        item.id === selectedMenuItem.id ? menuItemData : item
      ));
    } else {
      // Add new item
      setMenuItems(prev => [...prev, menuItemData]);
    }
  };

  const handleDeleteMenuItem = (id: string) => {
    setMenuItems(prev => prev.filter(item => item.id !== id));
    useToastToast({
      title: 'Menu dihapus!',
      description: 'Menu item berhasil dihapus dari daftar.',
    });
  };

  const handleToggleAvailability = (id: string) => {
    setMenuItems(prev => prev.map(item =>
      item.id === id ? { ...item, available: !item.available } : item
    ));

    const item = menuItems.find(item => item.id === id);
    useToastToast({
      title: item?.available ? 'Menu dinonaktifkan' : 'Menu diaktifkan',
      description: `${item?.name} sekarang ${item?.available ? 'tidak tersedia' : 'tersedia'}.`,
    });
  };

  const handleOrderStatusUpdate = async (orderId: string, newStatus: string) => {
    try {
      const response = await fetch(`http://localhost:5000/api/merchant/orders/${orderId}/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus,
          merchantId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update order status');
      }

      // Update local state
      setOrders(prev => prev.map(order =>
        order.id === orderId ? { ...order, status: newStatus } : order
      ));

      useToastToast({
        title: 'Status pesanan diupdate!',
        description: `Pesanan ${orderId} berhasil diupdate ke ${
          newStatus === 'processing' ? 'Sedang Diproses' :
          newStatus === 'ready' ? 'Siap Diambil' :
          newStatus === 'delivering' ? 'Sedang Diantar' :
          newStatus === 'delivered' ? 'Selesai' : newStatus
        }.`,
      });

      // Reload data to get fresh notifications
      loadDashboardData();
    } catch (error) {
      console.error('Error updating order status:', error);
      toast.error('Gagal mengupdate status pesanan');
    }
  };

  const handleAddDiscount = () => {
    setSelectedDiscount(null);
    setShowDiscountDialog(true);
  };

  const handleEditDiscount = (discount: any) => {
    setSelectedDiscount(discount);
    setShowDiscountDialog(true);
  };

  const handleSaveDiscount = (discountData: any) => {
    if (selectedDiscount) {
      setDiscounts(prev => prev.map(d =>
        d.id === selectedDiscount.id ? discountData : d
      ));
    } else {
      setDiscounts(prev => [...prev, discountData]);
    }
  };

  const handleDeleteDiscount = (id: string) => {
    setDiscounts(prev => prev.filter(d => d.id !== id));
    useToastToast({
      title: 'Diskon dihapus!',
      description: 'Diskon berhasil dihapus dari daftar.',
    });
  };

  const handleUpdateMerchantInfo = async (updatedInfo: any) => {
    try {
      // Update merchant info via API
      const response = await fetch(`http://localhost:5000/api/merchant/profile`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ merchantId, ...updatedInfo }),
      });

      if (!response.ok) throw new Error('Failed to update merchant info');

      setMerchantInfo(updatedInfo);
      setIsEditingInfo(false);
      toast.success('Informasi resto berhasil diperbarui');
    } catch (error) {
      console.error('Error updating merchant info:', error);
      toast.error('Gagal memperbarui informasi resto');
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(value);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Merchant Dashboard</h1>
            <p className="text-gray-600">{merchantInfo.business_name || 'Loading...'}</p>
            <p className="text-sm text-gray-500">{merchantInfo.business_address}</p>
          </div>
          <Button onClick={logout} variant="outline" className="flex items-center gap-2">
            <ArrowLeft className="h-4 w-4" />
            Logout
          </Button>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-blue-100">Total Pesanan</p>
                <p className="text-3xl font-bold">{analytics.totalOrders}</p>
                <p className="text-sm text-blue-100">{analytics.orderChange} dari bulan lalu</p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-green-100">Revenue Bulanan</p>
                <p className="text-3xl font-bold">{formatCurrency(analytics.monthlyRevenue)}</p>
                <p className="text-sm text-green-100">{analytics.revenueChange} dari bulan lalu</p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-orange-100">Waktu Persiapan</p>
                <p className="text-3xl font-bold">{analytics.avgPreparationTime} min</p>
                <p className="text-sm text-orange-100">{analytics.prepTimeChange} dari bulan lalu</p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-purple-100">Menu Aktif</p>
                <p className="text-3xl font-bold">{menuItems.filter(item => item.available).length}</p>
                <p className="text-sm text-purple-100">dari {menuItems.length} total menu</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="orders" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full max-w-md">
            <TabsTrigger value="orders">Pesanan</TabsTrigger>
            <TabsTrigger value="menu">Menu</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
          </TabsList>

          <TabsContent value="orders" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Pesanan Masuk
                </CardTitle>
                <CardDescription>Kelola pesanan yang masuk hari ini</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {orders.length === 0 ? (
                    <div className="text-center py-8">
                      <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">Belum ada pesanan hari ini</p>
                    </div>
                  ) : (
                    orders.map((order) => (
                      <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg bg-white">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <span className="font-medium">#{order.id}</span>
                            <Badge
                              variant={order.status === 'delivered' ? 'default' : 'secondary'}
                              className={
                                order.status === 'pending' ? 'bg-red-500 text-white' :
                                order.status === 'processing' ? 'bg-yellow-500 text-white' :
                                order.status === 'ready' ? 'bg-blue-500 text-white' :
                                order.status === 'delivering' ? 'bg-purple-500 text-white' :
                                'bg-green-500 text-white'
                              }
                            >
                              {order.status === 'pending' ? 'Pesanan Baru' :
                               order.status === 'processing' ? 'Sedang Dimasak' :
                               order.status === 'ready' ? 'Siap Diambil' :
                               order.status === 'delivering' ? 'Sedang Diantar' :
                               'Selesai'}
                            </Badge>
                            {order.status === 'pending' && (
                              <Badge variant="outline" className="bg-red-50 text-red-600 border-red-200 animate-pulse">
                                🔔 Perlu Diproses!
                              </Badge>
                            )}
                          </div>
                          <div className="space-y-1">
                            <p className="text-sm text-gray-600">
                              <strong>Customer:</strong> {order.customer_name || 'Customer'}
                            </p>
                            <p className="text-sm text-gray-600">
                              <strong>Items:</strong> {order.items_display || order.items || 'Detail pesanan tidak tersedia'}
                            </p>
                            <p className="text-sm text-gray-600">
                              <strong>Alamat:</strong> {order.delivery_address || 'Alamat tidak tersedia'}
                            </p>
                            <p className="text-xs text-gray-500">
                              Dipesan: {new Date(order.created_at).toLocaleString('id-ID')}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-lg">
                            {typeof order.total_amount === 'number'
                              ? formatCurrency(order.total_amount)
                              : order.total || 'Rp 0'
                            }
                          </p>
                          <div className="mt-2 space-y-1">
                            {order.status === 'pending' && (
                              <Button
                                size="sm"
                                className="bg-green-600 hover:bg-green-700 w-full"
                                onClick={() => handleOrderStatusUpdate(order.id, 'processing')}
                              >
                                ✅ Terima Pesanan
                              </Button>
                            )}
                            {order.status === 'processing' && (
                              <Button
                                size="sm"
                                className="bg-blue-600 hover:bg-blue-700 w-full"
                                onClick={() => handleOrderStatusUpdate(order.id, 'ready')}
                              >
                                🍽️ Tandai Siap
                              </Button>
                            )}
                            {order.status === 'ready' && (
                              <Button
                                size="sm"
                                variant="outline"
                                className="w-full"
                                onClick={() => handleOrderStatusUpdate(order.id, 'delivering')}
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Sudah Diambil Driver
                              </Button>
                            )}
                            {order.status === 'delivering' && (
                              <div className="text-center">
                                <p className="text-sm text-purple-600 font-medium">🚗 Sedang Diantar</p>
                                <p className="text-xs text-gray-500">Driver: {order.driver_name || 'Mencari driver...'}</p>
                              </div>
                            )}
                            {order.status === 'delivered' && (
                              <div className="text-center">
                                <p className="text-sm text-green-600 font-medium">✅ Selesai</p>
                                <p className="text-xs text-gray-500">
                                  Selesai: {new Date(order.updated_at).toLocaleString('id-ID')}
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="menu" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="flex items-center gap-2">
                    Menu Management
                  </span>
                  <div className="flex gap-2">
                    <Button onClick={handleAddDiscount} variant="outline" className="flex items-center gap-2">
                      <Tag className="h-4 w-4" />
                      Kelola Diskon
                    </Button>
                    <Button onClick={handleAddMenuItemDialog} className="bg-blue-600 hover:bg-blue-700">
                      <Plus className="h-4 w-4 mr-2" />
                      Tambah Menu
                    </Button>
                  </div>
                </CardTitle>
                <CardDescription>Kelola menu resto Anda</CardDescription>
              </CardHeader>
              <CardContent>
                {/* Discount List */}
                <div className="mb-6">
                  <h3 className="font-medium mb-3">Diskon Aktif</h3>
                  <div className="space-y-2">
                    {discounts.map((discount) => (
                      <div key={discount.id} className="flex items-center justify-between p-3 border rounded-lg bg-white">
                        <div>
                          <h4 className="font-medium">{discount.title}</h4>
                          <p className="text-sm text-gray-600">{discount.description}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant="outline">
                              {discount.type === 'percentage' ? `${discount.value}%` : `Rp ${discount.value.toLocaleString()}`}
                            </Badge>
                            <span className="text-sm text-gray-500">
                              Min. order: Rp {discount.minOrder.toLocaleString()}
                            </span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditDiscount(discount)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteDiscount(discount.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Menu List */}
                <div className="space-y-4">
                  {menuItems.map((item) => (
                    <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg bg-white">
                      <div className="flex gap-4">
                        <div className="w-20 h-20 bg-gray-100 rounded-lg flex items-center justify-center">
                          {item.image ? (
                            <img
                              src={item.image}
                              alt={item.name}
                              className="w-full h-full object-cover rounded-lg"
                            />
                          ) : (
                            <ImageIcon className="h-8 w-8 text-gray-400" />
                          )}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="font-medium text-lg">{item.name}</h3>
                            <Badge variant={item.available ? 'default' : 'secondary'}>
                              {item.available ? 'Tersedia' : 'Habis'}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-1">{item.description}</p>
                          <div className="flex items-center gap-4 text-sm">
                            <span className="font-medium text-green-600">Rp {parseInt(item.price).toLocaleString()}</span>
                            <span className="text-gray-500">• {item.category}</span>
                            <span className="text-gray-500">• {item.preparationTime} menit</span>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="flex items-center gap-2 mr-3">
                          {item.available ? <Eye className="h-4 w-4 text-green-500" /> : <EyeOff className="h-4 w-4 text-gray-400" />}
                          <Switch
                            checked={item.available}
                            onCheckedChange={() => handleToggleAvailability(item.id)}
                          />
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditMenuItem(item)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteMenuItem(item.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Analytics & Insights</CardTitle>
                <CardDescription>Performa restaurant dalam 30 hari terakhir</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div className="text-center p-6 border rounded-lg">
                    <h3 className="text-lg font-medium mb-2">Total Pesanan</h3>
                    <p className="text-3xl font-bold text-blue-600">{analytics.totalOrders}</p>
                    <p className="text-sm text-gray-600">{analytics.orderChange} dari bulan lalu</p>
                  </div>
                  <div className="text-center p-6 border rounded-lg">
                    <h3 className="text-lg font-medium mb-2">Revenue Bulanan</h3>
                    <p className="text-3xl font-bold text-green-600">{formatCurrency(analytics.monthlyRevenue)}</p>
                    <p className="text-sm text-gray-600">{analytics.revenueChange} dari bulan lalu</p>
                  </div>
                  <div className="text-center p-6 border rounded-lg">
                    <h3 className="text-lg font-medium mb-2">Menu Terpopuler</h3>
                    <p className="text-xl font-bold text-orange-600">
                      {analytics.popularItems.length > 0 ? analytics.popularItems[0].name : 'Belum ada data'}
                    </p>
                    <p className="text-sm text-gray-600">
                      {analytics.popularItems.length > 0 ? `${analytics.popularItems[0].order_count} pesanan bulan ini` : 'Tidak ada pesanan'}
                    </p>
                  </div>
                  <div className="text-center p-6 border rounded-lg">
                    <h3 className="text-lg font-medium mb-2">Waktu Persiapan Rata-rata</h3>
                    <p className="text-3xl font-bold text-purple-600">{analytics.avgPreparationTime} menit</p>
                    <p className="text-sm text-gray-600">{analytics.prepTimeChange} dari bulan lalu</p>
                  </div>
                </div>

                {/* Popular Items List */}
                {analytics.popularItems.length > 0 && (
                  <div>
                    <h3 className="text-lg font-medium mb-4">Menu Terpopuler</h3>
                    <div className="space-y-3">
                      {analytics.popularItems.map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-4 border rounded-lg bg-white">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                              <span className="text-sm font-bold text-blue-600">#{index + 1}</span>
                            </div>
                            <span className="font-medium">{item.name}</span>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-blue-600">{item.order_count} pesanan</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Pengaturan Resto</CardTitle>
                <CardDescription>Kelola informasi dan pengaturan resto Anda</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Informasi Resto */}
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="font-medium">Informasi Resto</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsEditingInfo(!isEditingInfo)}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      {isEditingInfo ? 'Batal' : 'Edit'}
                    </Button>
                  </div>

                  {isEditingInfo ? (
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor="business_name">Nama Resto</Label>
                        <Input
                          id="business_name"
                          value={merchantInfo.business_name}
                          onChange={(e) => setMerchantInfo(prev => ({ ...prev, business_name: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="business_address">Alamat</Label>
                        <Input
                          id="business_address"
                          value={merchantInfo.business_address}
                          onChange={(e) => setMerchantInfo(prev => ({ ...prev, business_address: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="phone">Nomor Telepon</Label>
                        <Input
                          id="phone"
                          value={merchantInfo.phone}
                          onChange={(e) => setMerchantInfo(prev => ({ ...prev, phone: e.target.value }))}
                        />
                      </div>
                      <div>
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          value={merchantInfo.email}
                          onChange={(e) => setMerchantInfo(prev => ({ ...prev, email: e.target.value }))}
                        />
                      </div>
                      <div className="flex gap-2">
                        <Button onClick={() => handleUpdateMerchantInfo(merchantInfo)}>
                          Simpan Perubahan
                        </Button>
                        <Button variant="outline" onClick={() => setIsEditingInfo(false)}>
                          Batal
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-2 text-sm">
                      <p><strong>Nama:</strong> {merchantInfo.business_name}</p>
                      <p><strong>Alamat:</strong> {merchantInfo.business_address}</p>
                      <p><strong>Telepon:</strong> {merchantInfo.phone}</p>
                      <p><strong>Email:</strong> {merchantInfo.email}</p>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-medium">Jam Operasional</h3>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsOperatingHoursDialogOpen(true)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                    </div>
                    <div className="space-y-2 text-sm">
                      {operatingHours.length > 0 ? (
                        operatingHours.map((hour, index) => (
                          <div key={index} className="flex justify-between">
                            <span>{hour.day_of_week}</span>
                            <span>
                              {hour.is_closed ? 'Tutup' : `${hour.open_time} - ${hour.close_time}`}
                            </span>
                          </div>
                        ))
                      ) : (
                        <p className="text-gray-500">Belum ada jam operasional</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium mb-3">Kategori Menu</h3>
                    <div className="space-y-2">
                      {categories.length > 0 ? (
                        categories.map((category, index) => (
                          <div key={index} className="flex items-center justify-between p-2 border rounded">
                            <span className="text-sm">{category.name}</span>
                            <Badge variant="outline">{category.is_active ? 'Aktif' : 'Nonaktif'}</Badge>
                          </div>
                        ))
                      ) : (
                        <p className="text-sm text-gray-500">Belum ada kategori</p>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsCategoryDialogOpen(true)}
                        className="w-full"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Tambah Kategori
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t">
                  <h3 className="font-medium mb-3">Pengaturan Notifikasi</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Notifikasi pesanan baru</span>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Email laporan harian</span>
                      <Switch defaultChecked />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">SMS reminder</span>
                      <Switch />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <EditMenuDialog
          open={editDialogOpen}
          onOpenChange={setEditDialogOpen}
          menuItem={selectedMenuItem}
          onSave={handleSaveMenuItem}
        />

        <DiscountDialog
          open={showDiscountDialog}
          onOpenChange={setShowDiscountDialog}
          discount={selectedDiscount}
          onSave={handleSaveDiscount}
        />

        <MenuCategoryDialog
          open={isCategoryDialogOpen}
          onOpenChange={setIsCategoryDialogOpen}
          onSave={handleAddCategory}
        />

        <OperatingHoursDialog
          open={isOperatingHoursDialogOpen}
          onOpenChange={setIsOperatingHoursDialogOpen}
          hours={operatingHours}
          onSave={handleUpdateOperatingHours}
        />

        <MenuItemDialog
          open={isMenuItemDialogOpen}
          onOpenChange={setIsMenuItemDialogOpen}
          categories={categories}
          onSave={handleAddMenuItem}
        />
      </div>
    </div>
  );
};

export default MerchantDashboard;
