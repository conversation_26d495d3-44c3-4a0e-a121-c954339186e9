const { db } = require('./database-setup');

const seedData = () => {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      // Clear existing data
      db.run('DELETE FROM activity_logs');
      db.run('DELETE FROM notifications');
      db.run('DELETE FROM merchant_reviews');
      db.run('DELETE FROM operating_hours');
      db.run('DELETE FROM menu_categories');
      db.run('DELETE FROM transaction_items');
      db.run('DELETE FROM menu_items');
      db.run('DELETE FROM transactions');
      db.run('DELETE FROM users');

      // Insert Users
      const users = [
        {
          id: 'merchant-1',
          name: '<PERSON><PERSON>',
          email: '<EMAIL>',
          password: 'password123',
          phone: '+62 811 3333 3333',
          role: 'merchant',
          business_name: 'Warung Padang Sederhana',
          status: 'active',
          business_address: 'Jl. Sudirman No. 123, Jakarta Pusat',
          email_verified: 1,
          phone_verified: 1
        },
        {
          id: 'customer-1',
          name: '<PERSON><PERSON>',
          email: '<EMAIL>',
          password: 'password123',
          phone: '+62 811 2222 2222',
          role: 'customer',
          status: 'active',
          email_verified: 1,
          phone_verified: 1
        },
        {
          id: 'customer-2',
          name: 'Ahmad Rahman',
          email: '<EMAIL>',
          password: 'password123',
          phone: '+62 812 4444 4444',
          role: 'customer',
          status: 'active',
          email_verified: 1,
          phone_verified: 1
        },
        {
          id: 'driver-1',
          name: 'Joko Widodo',
          email: '<EMAIL>',
          password: 'password123',
          phone: '+62 813 5555 5555',
          role: 'driver',
          vehicle_type: 'motorcycle',
          vehicle_number: 'B 1234 ABC',
          status: 'active',
          email_verified: 1,
          phone_verified: 1
        }
      ];

      users.forEach(user => {
        db.run(`INSERT INTO users (id, name, email, password, phone, role, business_name, status, business_address, vehicle_type, vehicle_number, email_verified, phone_verified)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [user.id, user.name, user.email, user.password, user.phone, user.role, user.business_name || null, user.status, user.business_address || null, user.vehicle_type || null, user.vehicle_number || null, user.email_verified, user.phone_verified]);
      });

      // Insert Menu Categories
      const categories = [
        {
          id: 'cat-1',
          merchant_id: 'merchant-1',
          name: 'Makanan Utama',
          description: 'Menu makanan utama khas Padang',
          is_active: 1
        },
        {
          id: 'cat-2',
          merchant_id: 'merchant-1',
          name: 'Minuman',
          description: 'Minuman segar dan tradisional',
          is_active: 1
        }
      ];

      categories.forEach(cat => {
        db.run(`INSERT INTO menu_categories (id, merchant_id, name, description, is_active) VALUES (?, ?, ?, ?, ?)`,
          [cat.id, cat.merchant_id, cat.name, cat.description, cat.is_active]);
      });

      // Insert Menu Items
      const menuItems = [
        {
          id: 'menu-1',
          merchant_id: 'merchant-1',
          name: 'Rendang Daging',
          price: 25000,
          description: 'Rendang daging sapi dengan bumbu tradisional Padang yang kaya rempah',
          is_available: 1
        },
        {
          id: 'menu-2',
          merchant_id: 'merchant-1',
          name: 'Ayam Gulai',
          price: 20000,
          description: 'Ayam gulai dengan santan kelapa dan bumbu kuning yang gurih',
          is_available: 1
        },
        {
          id: 'menu-3',
          merchant_id: 'merchant-1',
          name: 'Nasi Padang Komplit',
          price: 35000,
          description: 'Nasi putih dengan rendang, ayam gulai, sayur nangka, dan sambal',
          is_available: 1
        },
        {
          id: 'menu-4',
          merchant_id: 'merchant-1',
          name: 'Sate Padang',
          price: 18000,
          description: 'Sate daging sapi dengan kuah kacang khas Padang yang pedas',
          is_available: 1
        },
        {
          id: 'menu-5',
          merchant_id: 'merchant-1',
          name: 'Dendeng Balado',
          price: 28000,
          description: 'Dendeng daging sapi dengan sambal balado pedas dan gurih',
          is_available: 1
        },
        {
          id: 'menu-6',
          merchant_id: 'merchant-1',
          name: 'Es Teh Manis',
          price: 5000,
          description: 'Es teh manis segar untuk menemani makanan pedas',
          is_available: 1
        },
        {
          id: 'menu-7',
          merchant_id: 'merchant-1',
          name: 'Es Jeruk',
          price: 8000,
          description: 'Es jeruk segar dengan potongan jeruk asli',
          is_available: 1
        }
      ];

      menuItems.forEach(item => {
        db.run(`INSERT INTO menu_items (id, merchant_id, name, price, description, is_available) VALUES (?, ?, ?, ?, ?, ?)`,
          [item.id, item.merchant_id, item.name, item.price, item.description, item.is_available]);
      });

      // Insert Operating Hours
      const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
      days.forEach((day, index) => {
        db.run(`INSERT INTO operating_hours (id, merchant_id, day_of_week, open_time, close_time, is_closed) VALUES (?, ?, ?, ?, ?, ?)`,
          [`hours-${index + 1}`, 'merchant-1', day, '08:00:00', '22:00:00', 0]);
      });

      // Insert Transactions
      const transactions = [
        {
          id: 'trans-1',
          user_id: 'customer-1',
          merchant_id: 'merchant-1',
          driver_id: 'driver-1',
          total_amount: 50000,
          delivery_fee: 10000,
          status: 'delivered',
          payment_status: 'paid',
          rating: 4.5,
          delivery_address: 'Jl. Thamrin No. 45, Jakarta Pusat',
          created_at: '2024-01-15 10:30:00',
          updated_at: '2024-01-15 11:15:00',
          actual_delivery_time: '2024-01-15 11:15:00'
        },
        {
          id: 'trans-2',
          user_id: 'customer-2',
          merchant_id: 'merchant-1',
          driver_id: 'driver-1',
          total_amount: 75000,
          delivery_fee: 10000,
          status: 'delivered',
          payment_status: 'paid',
          rating: 4.8,
          delivery_address: 'Jl. Gatot Subroto No. 88, Jakarta Selatan',
          created_at: '2024-01-14 14:20:00',
          updated_at: '2024-01-14 15:10:00',
          actual_delivery_time: '2024-01-14 15:10:00'
        },
        {
          id: 'trans-3',
          user_id: 'customer-1',
          merchant_id: 'merchant-1',
          driver_id: 'driver-1',
          total_amount: 35000,
          delivery_fee: 8000,
          status: 'delivered',
          payment_status: 'paid',
          rating: 4.2,
          delivery_address: 'Jl. Thamrin No. 45, Jakarta Pusat',
          created_at: '2024-01-13 12:30:00',
          updated_at: '2024-01-13 13:20:00',
          actual_delivery_time: '2024-01-13 13:20:00'
        },
        {
          id: 'trans-4',
          user_id: 'customer-2',
          merchant_id: 'merchant-1',
          total_amount: 28000,
          delivery_fee: 8000,
          status: 'processing',
          payment_status: 'paid',
          delivery_address: 'Jl. Gatot Subroto No. 88, Jakarta Selatan',
          created_at: '2024-01-16 09:15:00',
          updated_at: '2024-01-16 09:15:00'
        }
      ];

      transactions.forEach(trans => {
        db.run(`INSERT INTO transactions (id, user_id, merchant_id, driver_id, total_amount, delivery_fee, status, payment_status, rating, delivery_address, created_at, updated_at, actual_delivery_time)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [trans.id, trans.user_id, trans.merchant_id, trans.driver_id || null, trans.total_amount, trans.delivery_fee, trans.status, trans.payment_status, trans.rating || null, trans.delivery_address, trans.created_at, trans.updated_at, trans.actual_delivery_time || null]);
      });

      // Insert Transaction Items
      const transactionItems = [
        { id: 'item-1', transaction_id: 'trans-1', menu_name: 'Nasi Padang Komplit', quantity: 1, price: 35000 },
        { id: 'item-2', transaction_id: 'trans-1', menu_name: 'Es Teh Manis', quantity: 2, price: 5000 },
        { id: 'item-3', transaction_id: 'trans-2', menu_name: 'Rendang Daging', quantity: 2, price: 25000 },
        { id: 'item-4', transaction_id: 'trans-2', menu_name: 'Ayam Gulai', quantity: 1, price: 20000 },
        { id: 'item-5', transaction_id: 'trans-2', menu_name: 'Es Jeruk', quantity: 1, price: 8000 },
        { id: 'item-6', transaction_id: 'trans-3', menu_name: 'Sate Padang', quantity: 1, price: 18000 },
        { id: 'item-7', transaction_id: 'trans-3', menu_name: 'Dendeng Balado', quantity: 1, price: 28000 },
        { id: 'item-8', transaction_id: 'trans-4', menu_name: 'Ayam Gulai', quantity: 1, price: 20000 },
        { id: 'item-9', transaction_id: 'trans-4', menu_name: 'Es Teh Manis', quantity: 1, price: 5000 }
      ];

      transactionItems.forEach(item => {
        db.run(`INSERT INTO transaction_items (id, transaction_id, menu_name, quantity, price) VALUES (?, ?, ?, ?, ?)`,
          [item.id, item.transaction_id, item.menu_name, item.quantity, item.price]);
      });

      // Insert Merchant Reviews
      const reviews = [
        {
          id: 'review-1',
          user_id: 'customer-1',
          merchant_id: 'merchant-1',
          transaction_id: 'trans-1',
          rating: 4.5,
          comment: 'Makanannya enak dan pelayanan cepat. Rendangnya sangat lezat!',
          food_rating: 4.5,
          service_rating: 4.0,
          delivery_rating: 5.0,
          created_at: '2024-01-15 11:30:00'
        },
        {
          id: 'review-2',
          user_id: 'customer-2',
          merchant_id: 'merchant-1',
          transaction_id: 'trans-2',
          rating: 4.8,
          comment: 'Rendangnya mantap, bumbunya pas. Ayam gulainya juga enak banget!',
          food_rating: 5.0,
          service_rating: 4.5,
          delivery_rating: 4.5,
          created_at: '2024-01-14 15:30:00'
        },
        {
          id: 'review-3',
          user_id: 'customer-1',
          merchant_id: 'merchant-1',
          transaction_id: 'trans-3',
          rating: 4.2,
          comment: 'Pengiriman cepat, makanan masih hangat. Sate Padangnya juara!',
          food_rating: 4.0,
          service_rating: 4.5,
          delivery_rating: 4.0,
          created_at: '2024-01-13 13:45:00'
        }
      ];

      reviews.forEach(review => {
        db.run(`INSERT INTO merchant_reviews (id, user_id, merchant_id, transaction_id, rating, comment, food_rating, service_rating, delivery_rating, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [review.id, review.user_id, review.merchant_id, review.transaction_id, review.rating, review.comment, review.food_rating, review.service_rating, review.delivery_rating, review.created_at]);
      });

      // Insert Notifications
      const notifications = [
        {
          id: 'notif-1',
          user_id: 'merchant-1',
          title: 'Pesanan Baru',
          message: 'Anda mendapat pesanan baru dari Ahmad Rahman',
          type: 'order',
          is_read: 0,
          created_at: '2024-01-16 09:15:00'
        },
        {
          id: 'notif-2',
          user_id: 'merchant-1',
          title: 'Review Baru',
          message: 'Siti Nurhaliza memberikan review 4.2 bintang untuk pesanan Anda',
          type: 'system',
          is_read: 0,
          created_at: '2024-01-13 13:45:00'
        },
        {
          id: 'notif-3',
          user_id: 'merchant-1',
          title: 'Promosi Berakhir',
          message: 'Promosi diskon 20% akan berakhir dalam 2 hari',
          type: 'promotion',
          is_read: 1,
          created_at: '2024-01-12 08:00:00'
        },
        {
          id: 'notif-4',
          user_id: 'merchant-1',
          title: 'Pesanan Selesai',
          message: 'Pesanan dari Siti Nurhaliza telah selesai diantar',
          type: 'order',
          is_read: 1,
          created_at: '2024-01-15 11:15:00'
        }
      ];

      notifications.forEach(notif => {
        db.run(`INSERT INTO notifications (id, user_id, title, message, type, is_read, created_at) VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [notif.id, notif.user_id, notif.title, notif.message, notif.type, notif.is_read, notif.created_at]);
      });

      console.log('Sample data inserted successfully');
      resolve();
    });
  });
};

module.exports = { seedData };
