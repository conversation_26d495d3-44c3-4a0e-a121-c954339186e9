import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, MapPin, Phone, Navigation } from "lucide-react";

interface Order {
  id: string;
  restaurant: {
    name: string;
    address: string;
    phone: string;
  };
  customer: {
    name: string;
    address: string;
    phone: string;
  };
  date: string;
  total: number;
  status: 'pending' | 'accepted' | 'picked_up' | 'delivered' | 'cancelled';
  items: {
    name: string;
    quantity: number;
    price: number;
  }[];
  estimatedTime?: string;
  distance?: string;
}

interface OrderListProps {
  orders: Order[];
  onAccept?: (order: Order) => void;
  onUpdateStatus?: (order: Order, status: Order['status']) => void;
  onCall?: (phone: string) => void;
  onNavigate?: (address: string) => void;
}

const getStatusColor = (status: Order['status']) => {
  switch (status) {
    case 'pending':
      return 'bg-yellow-500';
    case 'accepted':
      return 'bg-blue-500';
    case 'picked_up':
      return 'bg-purple-500';
    case 'delivered':
      return 'bg-green-500';
    case 'cancelled':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

const getStatusText = (status: Order['status']) => {
  switch (status) {
    case 'pending':
      return 'Menunggu Konfirmasi';
    case 'accepted':
      return 'Diterima';
    case 'picked_up':
      return 'Sudah Diambil';
    case 'delivered':
      return 'Selesai';
    case 'cancelled':
      return 'Dibatalkan';
    default:
      return status;
  }
};

export const OrderList: React.FC<OrderListProps> = ({
  orders,
  onAccept,
  onUpdateStatus,
  onCall,
  onNavigate
}) => {
  if (!orders || orders.length === 0) {
    return (
      <div className="text-center py-8">
        <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-600">Tidak ada pesanan tersedia</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {orders.map((order) => (
        <Card key={order.id}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Pesanan #{order.id}</CardTitle>
                <p className="text-sm text-gray-500">
                  {new Date(order.created_at || order.date).toLocaleString('id-ID')}
                </p>
              </div>
              <Badge className={getStatusColor(order.status)}>
                {getStatusText(order.status)}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Restaurant Information */}
              <div>
                <h4 className="font-medium mb-2">🏪 Restoran</h4>
                <div className="space-y-2">
                  <p className="text-sm font-medium">{order.merchant_name || 'Restaurant'}</p>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <MapPin className="h-4 w-4" />
                    <span>{order.merchant_address || 'Alamat restoran tidak tersedia'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {order.merchant_phone && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2"
                        onClick={() => onCall?.(order.merchant_phone)}
                      >
                        <Phone className="h-4 w-4" />
                        {order.merchant_phone}
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => onNavigate?.(order.merchant_address || 'Restaurant Address')}
                    >
                      <Navigation className="h-4 w-4" />
                      Navigasi
                    </Button>
                  </div>
                </div>
              </div>

              {/* Customer Information */}
              <div>
                <h4 className="font-medium mb-2">👤 Pelanggan</h4>
                <div className="space-y-2">
                  <p className="text-sm font-medium">{order.customer_name || 'Customer'}</p>
                  <div className="flex items-center gap-2 text-sm text-gray-500">
                    <MapPin className="h-4 w-4" />
                    <span>{order.customer_address || order.delivery_address || 'Alamat pengiriman tidak tersedia'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {order.customer_phone && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2"
                        onClick={() => onCall?.(order.customer_phone)}
                      >
                        <Phone className="h-4 w-4" />
                        {order.customer_phone}
                      </Button>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex items-center gap-2"
                      onClick={() => onNavigate?.(order.customer_address || order.delivery_address || 'Customer Address')}
                    >
                      <Navigation className="h-4 w-4" />
                      Navigasi
                    </Button>
                  </div>
                </div>
              </div>

              {/* Order Details */}
              <div>
                <h4 className="font-medium mb-2">🍽️ Detail Pesanan</h4>
                <div className="space-y-2">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <p className="text-sm">{order.items_display || order.items || 'Detail pesanan tidak tersedia'}</p>
                  </div>
                  <div className="border-t pt-2 mt-2">
                    <div className="flex items-center justify-between font-medium">
                      <span>Total Pesanan</span>
                      <span>
                        {typeof order.total_amount === 'number'
                          ? new Intl.NumberFormat('id-ID', {
                              style: 'currency',
                              currency: 'IDR',
                              minimumFractionDigits: 0,
                            }).format(order.total_amount)
                          : order.total || 'Rp 0'
                        }
                      </span>
                    </div>
                    {order.delivery_fee && (
                      <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>Ongkos Kirim</span>
                        <span>
                          {new Intl.NumberFormat('id-ID', {
                            style: 'currency',
                            currency: 'IDR',
                            minimumFractionDigits: 0,
                          }).format(order.delivery_fee)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Order Actions */}
              <div className="flex gap-2">
                {(order.status === 'ready' || order.status === 'processing') && onAccept && (
                  <Button
                    className="flex-1 bg-green-600 hover:bg-green-700"
                    onClick={() => onAccept(order)}
                  >
                    🚗 Terima Pesanan
                  </Button>
                )}
                {order.status === 'delivering' && onUpdateStatus && (
                  <Button
                    className="flex-1 bg-blue-600 hover:bg-blue-700"
                    onClick={() => onUpdateStatus(order.id, 'delivered')}
                  >
                    ✅ Selesai Diantar
                  </Button>
                )}
                {order.status === 'delivered' && (
                  <div className="flex-1 text-center py-2">
                    <span className="text-green-600 font-medium">✅ Pesanan Selesai</span>
                  </div>
                )}
              </div>

              {/* Additional Information */}
              {(order.estimated_time || order.distance) && (
                <div className="flex items-center gap-4 text-sm text-gray-500 bg-blue-50 p-3 rounded-lg">
                  {order.estimated_time && (
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      <span>Estimasi: {order.estimated_time}</span>
                    </div>
                  )}
                  {order.distance && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span>Jarak: {order.distance}</span>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};