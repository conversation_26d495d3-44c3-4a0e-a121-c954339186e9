# 📱 KIKAZEN-SHIP - Android Build Guide

## 🚀 Cara Build Aplikasi untuk Android

### **Opsi 1: PWA (Progressive Web App) - TERMUDAH**

Aplikasi sudah dikonfigurasi sebagai PWA dan bisa diinstall langsung dari browser:

1. **Buka di Browser Mobile**:
   ```
   http://localhost:5173
   ```

2. **Install PWA**:
   - Di Chrome Android: Tap menu → "Add to Home screen"
   - Di Safari iOS: Tap share → "Add to Home Screen"

3. **Fitur PWA**:
   - ✅ Install seperti native app
   - ✅ Offline support
   - ✅ Push notifications
   - ✅ Full screen mode
   - ✅ App icon di home screen

### **Opsi 2: Capacitor Android App**

#### **Prerequisites**:
- Android Studio terinstall
- Java JDK 11 atau lebih tinggi
- Android SDK

#### **Build Steps**:

1. **Build Web App**:
   ```bash
   npm run build
   ```

2. **Open Android Studio**:
   ```bash
   npm run build:android
   ```

3. **Build APK Debug**:
   ```bash
   npm run build:apk
   ```

4. **Build APK Release**:
   ```bash
   npm run build:apk-release
   ```

#### **Manual Build di Android Studio**:

1. **Sync Project**:
   ```bash
   npx cap sync android
   ```

2. **Open Android Studio**:
   ```bash
   npx cap open android
   ```

3. **Build di Android Studio**:
   - Build → Build Bundle(s) / APK(s) → Build APK(s)
   - APK akan tersimpan di: `android/app/build/outputs/apk/`

### **Opsi 3: Cordova (Alternative)**

1. **Install Cordova**:
   ```bash
   npm install -g cordova
   ```

2. **Create Cordova Project**:
   ```bash
   cordova create kikazen-ship-mobile com.kikazen.ship "KIKAZEN-SHIP"
   cd kikazen-ship-mobile
   cordova platform add android
   ```

3. **Copy Build Files**:
   ```bash
   # Build React app
   npm run build
   
   # Copy to Cordova
   cp -r ../dist/* www/
   ```

4. **Build APK**:
   ```bash
   cordova build android
   ```

## 📁 File Locations

- **APK Debug**: `android/app/build/outputs/apk/debug/`
- **APK Release**: `android/app/build/outputs/apk/release/`
- **PWA Manifest**: `public/manifest.json`
- **Capacitor Config**: `capacitor.config.ts`

## 🔧 Configuration

### **App Details**:
- **App Name**: KIKAZEN-SHIP
- **Package ID**: com.kikazen.ship
- **Version**: 1.0.0

### **Permissions** (Android):
- Internet access
- Network state
- Location (for delivery tracking)
- Camera (for QR codes)
- Storage (for offline data)

## 🎯 Deployment Options

1. **Google Play Store**: Upload APK release
2. **Direct Install**: Share APK file
3. **PWA**: Deploy web version, users install from browser
4. **Internal Distribution**: Use Firebase App Distribution

## 🛠️ Troubleshooting

### **Common Issues**:

1. **Gradle Build Failed**:
   ```bash
   cd android
   ./gradlew clean
   ./gradlew build
   ```

2. **Capacitor Sync Issues**:
   ```bash
   npx cap clean android
   npx cap add android
   npx cap sync android
   ```

3. **PWA Not Installing**:
   - Check HTTPS (required for PWA)
   - Verify manifest.json
   - Check service worker registration

## 📱 Testing

1. **PWA Testing**:
   - Chrome DevTools → Application → Manifest
   - Test offline functionality
   - Test "Add to Home Screen"

2. **Android Testing**:
   - Install APK on device
   - Test all features
   - Check permissions

## 🚀 Quick Start

**Untuk build APK langsung**:
```bash
# Install dependencies
npm install

# Build for Android
npm run build:apk

# APK akan tersedia di: android/app/build/outputs/apk/debug/
```

**Untuk PWA**:
```bash
# Build web app
npm run build

# Serve locally
npm run preview

# Buka di browser mobile dan "Add to Home Screen"
```

## 📞 Support

Jika ada masalah dalam build process, pastikan:
- ✅ Node.js versi 18+
- ✅ Android Studio terinstall
- ✅ Java JDK 11+
- ✅ Android SDK tools
- ✅ Gradle wrapper executable

**KIKAZEN-SHIP siap untuk deployment di Android!** 🎉
