import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Image as ImageIcon, Upload, X } from 'lucide-react';

const menuItemSchema = z.object({
  name: z.string().min(1, 'Nama menu harus diisi'),
  description: z.string().min(1, 'Deskripsi harus diisi'),
  price: z.string().min(1, 'Harga harus diisi'),
  category: z.string().min(1, 'Kategori harus diisi'),
  preparationTime: z.string().min(1, 'Waktu persiapan harus diisi'),
  image: z.string().optional(),
});

type MenuItemFormData = z.infer<typeof menuItemSchema>;

interface MenuItem {
  id: string;
  name: string;
  description: string;
  price: string | number;
  category: string;
  preparationTime?: number;
  preparation_time?: number; // Support both formats
  available?: boolean;
  is_available?: boolean; // Support both formats
  image?: string;
}

interface EditMenuDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  menuItem: MenuItem | null;
  onSave: (data: MenuItem) => void;
}

const EditMenuDialog: React.FC<EditMenuDialogProps> = ({
  open,
  onOpenChange,
  menuItem,
  onSave,
}) => {
  const { toast } = useToast();
  const [imagePreview, setImagePreview] = useState<string | null>(menuItem?.image || null);

  const getPreparationTime = (item: MenuItem | null) => {
    if (!item) return '15';
    return (item.preparationTime || item.preparation_time || 15).toString();
  };

  const getPrice = (item: MenuItem | null) => {
    if (!item) return '';
    return typeof item.price === 'number' ? item.price.toString() : item.price || '';
  };

  const getAvailability = (item: MenuItem | null) => {
    if (!item) return true;
    return item.available !== undefined ? item.available : (item.is_available !== undefined ? item.is_available : true);
  };

  const form = useForm<MenuItemFormData>({
    resolver: zodResolver(menuItemSchema),
    defaultValues: {
      name: menuItem?.name || '',
      description: menuItem?.description || '',
      price: getPrice(menuItem),
      category: menuItem?.category || '',
      preparationTime: getPreparationTime(menuItem),
      image: menuItem?.image || '',
    },
  });

  React.useEffect(() => {
    if (menuItem) {
      form.reset({
        name: menuItem.name || '',
        description: menuItem.description || '',
        price: getPrice(menuItem),
        category: menuItem.category || '',
        preparationTime: getPreparationTime(menuItem),
        image: menuItem.image || '',
      });
      setImagePreview(menuItem.image || null);
    } else {
      form.reset({
        name: '',
        description: '',
        price: '',
        category: '',
        preparationTime: '15',
        image: '',
      });
      setImagePreview(null);
    }
  }, [menuItem, form]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64String = reader.result as string;
        setImagePreview(base64String);
        form.setValue('image', base64String);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveImage = () => {
    setImagePreview(null);
    form.setValue('image', '');
  };

  const onSubmit = (data: MenuItemFormData) => {
    const updatedMenuItem: MenuItem = {
      id: menuItem?.id || Date.now().toString(),
      name: data.name,
      description: data.description,
      price: data.price,
      category: data.category,
      preparationTime: parseInt(data.preparationTime),
      available: getAvailability(menuItem),
      image: data.image || imagePreview || '/api/placeholder/300/200',
    };

    onSave(updatedMenuItem);
    toast({
      title: menuItem ? 'Menu berhasil diupdate!' : 'Menu berhasil ditambahkan!',
      description: `${data.name} telah ${menuItem ? 'diupdate' : 'ditambahkan'} ke menu.`,
    });
    onOpenChange(false);
    form.reset();
    setImagePreview(null);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>
            {menuItem ? 'Edit Menu Item' : 'Tambah Menu Baru'}
          </DialogTitle>
          <DialogDescription>
            {menuItem ? 'Edit informasi menu item' : 'Tambahkan menu baru ke resto Anda'}
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            {/* Image Upload */}
            <div className="space-y-2">
              <FormLabel>Gambar Menu</FormLabel>
              <div className="flex items-center gap-4">
                <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                  {imagePreview ? (
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <ImageIcon className="h-8 w-8 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <Input
                    type="file"
                    accept="image/*"
                    onChange={handleImageChange}
                    className="hidden"
                    id="image-upload"
                  />
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      className="flex-1"
                      onClick={() => document.getElementById('image-upload')?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload
                    </Button>
                    {imagePreview && (
                      <Button
                        type="button"
                        variant="outline"
                        className="flex-1"
                        onClick={handleRemoveImage}
                      >
                        <X className="h-4 w-4 mr-2" />
                        Hapus
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nama Menu</FormLabel>
                  <FormControl>
                    <Input placeholder="Contoh: Nasi Padang" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Deskripsi</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Deskripsikan menu Anda..."
                      className="min-h-[80px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Harga (Rp)</FormLabel>
                    <FormControl>
                      <Input placeholder="25000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="preparationTime"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Waktu (menit)</FormLabel>
                    <FormControl>
                      <Input placeholder="15" type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Kategori</FormLabel>
                  <FormControl>
                    <Input placeholder="Main Course, Beverage, dll" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                className="flex-1"
              >
                Batal
              </Button>
              <Button type="submit" className="flex-1 bg-blue-600 hover:bg-blue-700">
                {menuItem ? 'Update' : 'Tambah'} Menu
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditMenuDialog;
