import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  FileText, 
  Download, 
  Calendar, 
  DollarSign, 
  Users, 
  Store, 
  Car,
  Receipt,
  Filter,
  Search
} from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface PaymentReport {
  id: string;
  user_id: string;
  user_name: string;
  user_role: 'merchant' | 'driver';
  business_name?: string;
  total_earnings: number;
  total_transactions: number;
  commission_rate: number;
  commission_amount: number;
  net_payment: number;
  period_start: string;
  period_end: string;
  status: 'pending' | 'paid' | 'processing';
  payment_date?: string;
  invoice_number?: string;
}

interface PaymentReportsCardProps {
  onGenerateInvoice: (reportId: string) => Promise<void>;
}

const PaymentReportsCard: React.FC<PaymentReportsCardProps> = ({ onGenerateInvoice }) => {
  const [reports, setReports] = useState<PaymentReport[]>([]);
  const [filteredReports, setFilteredReports] = useState<PaymentReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'merchant' | 'driver'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'paid' | 'processing'>('all');
  const [dateRange, setDateRange] = useState('30d');

  useEffect(() => {
    loadPaymentReports();
  }, [dateRange]);

  useEffect(() => {
    filterReports();
  }, [reports, searchQuery, roleFilter, statusFilter]);

  const loadPaymentReports = async () => {
    try {
      setLoading(true);
      
      // Mock data - in real app, fetch from API
      const mockReports: PaymentReport[] = [
        {
          id: 'RPT001',
          user_id: 'USR001',
          user_name: 'Warung Padang Sederhana',
          user_role: 'merchant',
          business_name: 'Warung Padang Sederhana',
          total_earnings: 2500000,
          total_transactions: 45,
          commission_rate: 15,
          commission_amount: 375000,
          net_payment: 2125000,
          period_start: '2024-01-01',
          period_end: '2024-01-31',
          status: 'pending',
          invoice_number: 'INV-2024-001'
        },
        {
          id: 'RPT002',
          user_id: 'USR002',
          user_name: 'Ahmad Kurniawan',
          user_role: 'driver',
          total_earnings: 1200000,
          total_transactions: 120,
          commission_rate: 20,
          commission_amount: 240000,
          net_payment: 960000,
          period_start: '2024-01-01',
          period_end: '2024-01-31',
          status: 'paid',
          payment_date: '2024-02-01',
          invoice_number: 'INV-2024-002'
        },
        {
          id: 'RPT003',
          user_id: 'USR003',
          user_name: 'Bakso Malang Asli',
          user_role: 'merchant',
          business_name: 'Bakso Malang Asli',
          total_earnings: 1800000,
          total_transactions: 32,
          commission_rate: 15,
          commission_amount: 270000,
          net_payment: 1530000,
          period_start: '2024-01-01',
          period_end: '2024-01-31',
          status: 'processing',
          invoice_number: 'INV-2024-003'
        },
        {
          id: 'RPT004',
          user_id: 'USR004',
          user_name: 'Siti Nurhaliza',
          user_role: 'driver',
          total_earnings: 950000,
          total_transactions: 85,
          commission_rate: 20,
          commission_amount: 190000,
          net_payment: 760000,
          period_start: '2024-01-01',
          period_end: '2024-01-31',
          status: 'pending',
          invoice_number: 'INV-2024-004'
        }
      ];

      setReports(mockReports);
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal memuat laporan pembayaran",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const filterReports = () => {
    let filtered = reports;

    if (searchQuery) {
      filtered = filtered.filter(report => 
        report.user_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        report.business_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        report.invoice_number?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter(report => report.user_role === roleFilter);
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(report => report.status === statusFilter);
    }

    setFilteredReports(filtered);
  };

  const handleMarkAsPaid = async (reportId: string) => {
    try {
      setReports(prev => prev.map(report => 
        report.id === reportId 
          ? { ...report, status: 'paid' as const, payment_date: new Date().toISOString().split('T')[0] }
          : report
      ));
      
      toast({
        title: "Pembayaran Dikonfirmasi",
        description: "Status pembayaran berhasil diperbarui",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal mengkonfirmasi pembayaran",
        variant: "destructive"
      });
    }
  };

  const exportToExcel = async () => {
    try {
      // Create CSV content
      const headers = [
        'Invoice Number',
        'Nama',
        'Role',
        'Nama Bisnis',
        'Total Pendapatan',
        'Total Transaksi',
        'Rate Komisi (%)',
        'Komisi',
        'Pembayaran Bersih',
        'Periode Mulai',
        'Periode Selesai',
        'Status',
        'Tanggal Bayar'
      ];

      const csvContent = [
        headers.join(','),
        ...filteredReports.map(report => [
          report.invoice_number || '',
          `"${report.user_name}"`,
          report.user_role,
          `"${report.business_name || ''}"`,
          report.total_earnings,
          report.total_transactions,
          report.commission_rate,
          report.commission_amount,
          report.net_payment,
          report.period_start,
          report.period_end,
          report.status,
          report.payment_date || ''
        ].join(','))
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `payment-reports-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Export Berhasil",
        description: "Laporan pembayaran berhasil diekspor ke Excel",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal mengekspor laporan",
        variant: "destructive"
      });
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(value);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-100 text-green-800">Dibayar</Badge>;
      case 'processing':
        return <Badge className="bg-yellow-100 text-yellow-800">Diproses</Badge>;
      case 'pending':
        return <Badge className="bg-red-100 text-red-800">Pending</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getRoleIcon = (role: string) => {
    return role === 'merchant' ? <Store className="h-4 w-4" /> : <Car className="h-4 w-4" />;
  };

  const totalPendingPayments = filteredReports
    .filter(r => r.status === 'pending')
    .reduce((sum, r) => sum + r.net_payment, 0);

  const totalPaidPayments = filteredReports
    .filter(r => r.status === 'paid')
    .reduce((sum, r) => sum + r.net_payment, 0);

  return (
    <Card className="mb-8">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Laporan Pembayaran
            </CardTitle>
            <CardDescription>
              Kelola pembayaran untuk merchant dan driver
            </CardDescription>
          </div>
          <div className="flex gap-2">
            <Button
              onClick={exportToExcel}
              variant="outline"
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              Export Excel
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-red-600">Pending Pembayaran</p>
                <p className="text-2xl font-bold text-red-800">{formatCurrency(totalPendingPayments)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-red-600" />
            </div>
          </div>
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-green-600">Sudah Dibayar</p>
                <p className="text-2xl font-bold text-green-800">{formatCurrency(totalPaidPayments)}</p>
              </div>
              <Receipt className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-blue-600">Total Laporan</p>
                <p className="text-2xl font-bold text-blue-800">{filteredReports.length}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="flex flex-wrap gap-4 mb-6">
          <div className="flex-1 min-w-64">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Cari nama, bisnis, atau invoice..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          <Select value={roleFilter} onValueChange={(value) => setRoleFilter(value as any)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Role</SelectItem>
              <SelectItem value="merchant">Merchant</SelectItem>
              <SelectItem value="driver">Driver</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as any)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Semua Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="processing">Diproses</SelectItem>
              <SelectItem value="paid">Dibayar</SelectItem>
            </SelectContent>
          </Select>
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 Hari</SelectItem>
              <SelectItem value="30d">30 Hari</SelectItem>
              <SelectItem value="90d">90 Hari</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Reports Table */}
        <div className="space-y-4">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500 mx-auto mb-4"></div>
              <p>Memuat laporan pembayaran...</p>
            </div>
          ) : filteredReports.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">Tidak ada laporan pembayaran</p>
            </div>
          ) : (
            filteredReports.map((report) => (
              <div key={report.id} className="border rounded-lg p-4 bg-white">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {getRoleIcon(report.user_role)}
                    <div>
                      <h3 className="font-semibold">{report.user_name}</h3>
                      {report.business_name && (
                        <p className="text-sm text-gray-600">{report.business_name}</p>
                      )}
                      <p className="text-xs text-gray-500">
                        {report.invoice_number} • {report.period_start} - {report.period_end}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(report.status)}
                    <Badge variant="outline" className="capitalize">
                      {report.user_role}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <p className="text-xs text-gray-500">Total Pendapatan</p>
                    <p className="font-semibold">{formatCurrency(report.total_earnings)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Komisi ({report.commission_rate}%)</p>
                    <p className="font-semibold text-red-600">-{formatCurrency(report.commission_amount)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Pembayaran Bersih</p>
                    <p className="font-semibold text-green-600">{formatCurrency(report.net_payment)}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Total Transaksi</p>
                    <p className="font-semibold">{report.total_transactions}</p>
                  </div>
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    {report.payment_date && (
                      <span>Dibayar: {new Date(report.payment_date).toLocaleDateString('id-ID')}</span>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onGenerateInvoice(report.id)}
                      className="flex items-center gap-1"
                    >
                      <Receipt className="h-3 w-3" />
                      Invoice
                    </Button>
                    {report.status === 'pending' && (
                      <Button
                        size="sm"
                        onClick={() => handleMarkAsPaid(report.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Tandai Dibayar
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentReportsCard;
