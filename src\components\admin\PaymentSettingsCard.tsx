import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { CreditCard, QrCode, Building, Banknote, Settings, Save, Upload } from 'lucide-react';
import { toast } from '@/components/ui/use-toast';

interface PaymentSettings {
  qris_enabled: boolean;
  qris_merchant_id: string;
  qris_image_url: string;
  transfer_enabled: boolean;
  bank_accounts: Array<{
    bank_name: string;
    account_number: string;
    account_name: string;
  }>;
  cod_enabled: boolean;
  cod_fee: number;
  ewallet_enabled: boolean;
  ewallet_providers: string[];
}

interface PaymentSettingsCardProps {
  settings: PaymentSettings;
  onSave: (settings: PaymentSettings) => Promise<void>;
}

const PaymentSettingsCard: React.FC<PaymentSettingsCardProps> = ({ settings, onSave }) => {
  const [localSettings, setLocalSettings] = useState<PaymentSettings>(settings);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    try {
      setIsSaving(true);
      await onSave(localSettings);
      setIsEditing(false);
      toast({
        title: "Pengaturan Disimpan",
        description: "Pengaturan pembayaran berhasil diperbarui.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal menyimpan pengaturan pembayaran",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const addBankAccount = () => {
    setLocalSettings(prev => ({
      ...prev,
      bank_accounts: [
        ...prev.bank_accounts,
        { bank_name: '', account_number: '', account_name: '' }
      ]
    }));
  };

  const updateBankAccount = (index: number, field: string, value: string) => {
    setLocalSettings(prev => ({
      ...prev,
      bank_accounts: prev.bank_accounts.map((account, i) => 
        i === index ? { ...account, [field]: value } : account
      )
    }));
  };

  const removeBankAccount = (index: number) => {
    setLocalSettings(prev => ({
      ...prev,
      bank_accounts: prev.bank_accounts.filter((_, i) => i !== index)
    }));
  };

  return (
    <Card className="mb-8">
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Pengaturan Metode Pembayaran
            </CardTitle>
            <CardDescription>
              Kelola metode pembayaran yang tersedia untuk customer
            </CardDescription>
          </div>
          <div className="flex gap-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  onClick={() => {
                    setLocalSettings(settings);
                    setIsEditing(false);
                  }}
                >
                  Batal
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {isSaving ? 'Menyimpan...' : 'Simpan'}
                </Button>
              </>
            ) : (
              <Button
                onClick={() => setIsEditing(true)}
                variant="outline"
              >
                <Settings className="h-4 w-4 mr-2" />
                Edit Pengaturan
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* QRIS Settings */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <QrCode className="h-5 w-5 text-blue-600" />
                <div>
                  <h3 className="font-semibold">QRIS</h3>
                  <p className="text-sm text-gray-600">Quick Response Code Indonesian Standard</p>
                </div>
              </div>
              <Badge variant={localSettings.qris_enabled ? "default" : "secondary"}>
                {localSettings.qris_enabled ? "Aktif" : "Nonaktif"}
              </Badge>
            </div>
            
            {isEditing && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={localSettings.qris_enabled}
                    onChange={(e) => setLocalSettings(prev => ({
                      ...prev,
                      qris_enabled: e.target.checked
                    }))}
                    className="rounded"
                  />
                  <Label>Aktifkan QRIS</Label>
                </div>
                
                {localSettings.qris_enabled && (
                  <>
                    <div>
                      <Label>Merchant ID QRIS</Label>
                      <Input
                        value={localSettings.qris_merchant_id}
                        onChange={(e) => setLocalSettings(prev => ({
                          ...prev,
                          qris_merchant_id: e.target.value
                        }))}
                        placeholder="ID123456789"
                      />
                    </div>
                    <div>
                      <Label>URL Gambar QR Code</Label>
                      <Input
                        value={localSettings.qris_image_url}
                        onChange={(e) => setLocalSettings(prev => ({
                          ...prev,
                          qris_image_url: e.target.value
                        }))}
                        placeholder="https://example.com/qr-code.png"
                      />
                    </div>
                  </>
                )}
              </div>
            )}
            
            {!isEditing && localSettings.qris_enabled && (
              <div className="space-y-2 text-sm">
                <p><strong>Merchant ID:</strong> {localSettings.qris_merchant_id || 'Belum diatur'}</p>
                <p><strong>QR Code:</strong> {localSettings.qris_image_url ? 'Sudah diatur' : 'Belum diatur'}</p>
              </div>
            )}
          </div>

          {/* Transfer Bank Settings */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Building className="h-5 w-5 text-green-600" />
                <div>
                  <h3 className="font-semibold">Transfer Bank</h3>
                  <p className="text-sm text-gray-600">Transfer ke rekening bank</p>
                </div>
              </div>
              <Badge variant={localSettings.transfer_enabled ? "default" : "secondary"}>
                {localSettings.transfer_enabled ? "Aktif" : "Nonaktif"}
              </Badge>
            </div>
            
            {isEditing && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={localSettings.transfer_enabled}
                    onChange={(e) => setLocalSettings(prev => ({
                      ...prev,
                      transfer_enabled: e.target.checked
                    }))}
                    className="rounded"
                  />
                  <Label>Aktifkan Transfer Bank</Label>
                </div>
                
                {localSettings.transfer_enabled && (
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <Label>Rekening Bank</Label>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addBankAccount}
                      >
                        Tambah Rekening
                      </Button>
                    </div>
                    
                    {localSettings.bank_accounts.map((account, index) => (
                      <div key={index} className="border rounded p-3 space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Rekening {index + 1}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeBankAccount(index)}
                            className="text-red-600"
                          >
                            Hapus
                          </Button>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                          <Input
                            placeholder="Nama Bank"
                            value={account.bank_name}
                            onChange={(e) => updateBankAccount(index, 'bank_name', e.target.value)}
                          />
                          <Input
                            placeholder="No. Rekening"
                            value={account.account_number}
                            onChange={(e) => updateBankAccount(index, 'account_number', e.target.value)}
                          />
                          <Input
                            placeholder="Nama Pemilik"
                            value={account.account_name}
                            onChange={(e) => updateBankAccount(index, 'account_name', e.target.value)}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}
            
            {!isEditing && localSettings.transfer_enabled && (
              <div className="space-y-2">
                {localSettings.bank_accounts.map((account, index) => (
                  <div key={index} className="text-sm border rounded p-2">
                    <p><strong>{account.bank_name}</strong></p>
                    <p>{account.account_number} - {account.account_name}</p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* COD Settings */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <Banknote className="h-5 w-5 text-orange-600" />
                <div>
                  <h3 className="font-semibold">COD (Cash on Delivery)</h3>
                  <p className="text-sm text-gray-600">Bayar di tempat saat makanan tiba</p>
                </div>
              </div>
              <Badge variant={localSettings.cod_enabled ? "default" : "secondary"}>
                {localSettings.cod_enabled ? "Aktif" : "Nonaktif"}
              </Badge>
            </div>
            
            {isEditing && (
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={localSettings.cod_enabled}
                    onChange={(e) => setLocalSettings(prev => ({
                      ...prev,
                      cod_enabled: e.target.checked
                    }))}
                    className="rounded"
                  />
                  <Label>Aktifkan COD</Label>
                </div>
                
                {localSettings.cod_enabled && (
                  <div>
                    <Label>Biaya Tambahan COD (Rp)</Label>
                    <Input
                      type="number"
                      value={localSettings.cod_fee}
                      onChange={(e) => setLocalSettings(prev => ({
                        ...prev,
                        cod_fee: parseInt(e.target.value) || 0
                      }))}
                      placeholder="0"
                    />
                  </div>
                )}
              </div>
            )}
            
            {!isEditing && localSettings.cod_enabled && (
              <div className="text-sm">
                <p><strong>Biaya Tambahan:</strong> Rp {localSettings.cod_fee?.toLocaleString('id-ID') || 0}</p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentSettingsCard;
