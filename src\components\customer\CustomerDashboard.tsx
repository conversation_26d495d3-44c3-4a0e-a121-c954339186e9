import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Search, MapPin, Clock, User, MessageSquare, ShoppingCart, Plus, Minus, Trash2, X, Bell, Heart, Star, CreditCard } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { customerService } from '../../services/customerService';
import { paymentService } from '../../services/paymentService';
import { notificationService } from '../../services/notificationService';
import { RestaurantList } from './RestaurantList';
import { OrderList } from './OrderList';
import { ReviewDialog } from './ReviewDialog';
import { PaymentMethodDialog } from './PaymentMethodDialog';
import { toast } from 'sonner';

interface Restaurant {
  id: string;
  name: string;
  image: string;
  rating: number;
  deliveryTime: string;
  distance: string;
  cuisine: string;
  basePrice: number;
  deliveryFee: number;
  specialOffer?: string;
}

interface Order {
  id: string;
  restaurant: {
    name: string;
    image: string;
  };
  date: string;
  total: number;
  status: 'pending' | 'preparing' | 'on_the_way' | 'delivered' | 'cancelled';
  rating?: number;
  items: {
    name: string;
    quantity: number;
    price: number;
  }[];
  driver?: {
    name: string;
    phone: string;
  };
  estimatedTime?: string;
}

interface Review {
  id: string;
  orderId: string;
  rating: number;
  comment?: string;
  food_rating?: number;
  service_rating?: number;
  delivery_rating?: number;
  date: string;
}

interface PaymentMethod {
  id: string;
  type: 'credit_card' | 'debit_card' | 'e_wallet' | 'bank_transfer';
  provider: string;
  account_number?: string;
  is_default: boolean;
}

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'order' | 'payment' | 'system';
  is_read: boolean;
  created_at: string;
}

interface CartItem {
  id: string;
  name: string;
  price: number;
  quantity: number;
  restaurantId: string;
  restaurantName: string;
}

const CustomerDashboard = () => {
  const { logout, calculateCustomerPrice, user } = useAuth();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [cartItems, setCartItems] = useState<CartItem[]>([]);
  const [showCart, setShowCart] = useState(false);
  const [checkoutStep, setCheckoutStep] = useState<'cart' | 'address' | 'payment'>('cart');
  const [selectedRestaurant, setSelectedRestaurant] = useState<Restaurant | null>(null);
  const [showOrderDialog, setShowOrderDialog] = useState(false);
  const [selectedMenuItem, setSelectedMenuItem] = useState<any>(null);
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [favorites, setFavorites] = useState<Restaurant[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [reviews, setReviews] = useState<Review[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isReviewDialogOpen, setIsReviewDialogOpen] = useState(false);
  const [isPaymentMethodDialogOpen, setIsPaymentMethodDialogOpen] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);

  // Promo & Discount states
  const [promotions, setPromotions] = useState<any[]>([]);
  const [vouchers, setVouchers] = useState<any[]>([]);
  const [appliedPromo, setAppliedPromo] = useState<any>(null);
  const [promoCode, setPromoCode] = useState('');
  const [isPromoDialogOpen, setIsPromoDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const userId = user?.id || 'customer-1';

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Loading dashboard data for user:', userId);

      // Load restaurants first (most important)
      let restaurantData = [];
      try {
        restaurantData = await customerService.getRestaurants();
        console.log('Restaurants loaded:', restaurantData.length);
        setRestaurants(restaurantData);
      } catch (err) {
        console.error('Failed to load restaurants:', err);
        setError('Gagal memuat data restoran');
      }

      // Load other data in parallel
      try {
        const [favoriteData, orderData, reviewData, paymentData] = await Promise.all([
          customerService.getFavorites(userId).catch(err => {
            console.error('Failed to load favorites:', err);
            return [];
          }),
          customerService.getOrders(userId).catch(err => {
            console.error('Failed to load orders:', err);
            return [];
          }),
          customerService.getMyReviews(userId).catch(err => {
            console.error('Failed to load reviews:', err);
            return [];
          }),
          paymentService.getPaymentMethods(userId).catch(err => {
            console.error('Failed to load payment methods:', err);
            return [];
          })
        ]);

        setFavorites(favoriteData);
        setOrders(orderData);
        setReviews(reviewData);
        setPaymentMethods(paymentData);
      } catch (err) {
        console.error('Failed to load user data:', err);
      }

      // Load promotions and vouchers
      try {
        const [promotionData, voucherData] = await Promise.all([
          fetch('http://localhost:5000/api/customer/promotions')
            .then(res => res.json())
            .catch(err => {
              console.error('Failed to load promotions:', err);
              return [];
            }),
          fetch(`http://localhost:5000/api/customer/vouchers?userId=${userId}`)
            .then(res => res.json())
            .catch(err => {
              console.error('Failed to load vouchers:', err);
              return [];
            })
        ]);

        setPromotions(promotionData);
        setVouchers(voucherData);
        console.log('Promotions loaded:', promotionData.length);
      } catch (err) {
        console.error('Failed to load promo data:', err);
      }

      // Load notifications
      try {
        const notificationData = await notificationService.getNotifications(userId);
        setNotifications(notificationData);
        const count = await notificationService.getUnreadCount(userId);
        setUnreadCount(count);
      } catch (err) {
        console.error('Failed to load notifications:', err);
      }

    } catch (error) {
      console.error('Critical error loading dashboard data:', error);
      setError('Gagal memuat data dashboard');
      toast('Gagal memuat data dashboard', {
        description: 'Silakan refresh halaman atau coba lagi nanti',
        type: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleAddToFavorites = async (restaurantId: string) => {
    try {
      await customerService.addToFavorites(userId, restaurantId);
      toast('Restoran berhasil ditambahkan ke favorit', {
        type: 'success'
      });
      loadDashboardData();
    } catch (error) {
      console.error('Failed to add to favorites:', error);
      toast('Gagal menambahkan ke favorit', {
        description: 'Silakan coba lagi nanti',
        type: 'error'
      });
    }
  };

  const handleRemoveFromFavorites = async (restaurantId: string) => {
    try {
      await customerService.removeFromFavorites(userId, restaurantId);
      toast('Restoran berhasil dihapus dari favorit', {
        type: 'success'
      });
      loadDashboardData();
    } catch (error) {
      console.error('Failed to remove from favorites:', error);
      toast('Gagal menghapus dari favorit', {
        description: 'Silakan coba lagi nanti',
        type: 'error'
      });
    }
  };

  const handleAddReview = async (data: {
    rating: number;
    comment?: string;
    food_rating?: number;
    service_rating?: number;
    delivery_rating?: number;
  }) => {
    if (!selectedOrder) return;

    try {
      await customerService.addReview({
        userId,
        merchantId: selectedOrder.restaurant.id,
        ...data
      });
      toast('Ulasan berhasil ditambahkan', {
        type: 'success'
      });
      setIsReviewDialogOpen(false);
      loadDashboardData();
    } catch (error) {
      console.error('Failed to add review:', error);
      toast('Gagal menambahkan ulasan', {
        description: 'Silakan coba lagi nanti',
        type: 'error'
      });
    }
  };

  const handleAddPaymentMethod = async (data: {
    type: 'credit_card' | 'debit_card' | 'e_wallet' | 'bank_transfer';
    provider: string;
    account_number?: string;
  }) => {
    try {
      await paymentService.addPaymentMethod({
        userId,
        ...data
      });
      toast('Metode pembayaran berhasil ditambahkan', {
        type: 'success'
      });
      setIsPaymentMethodDialogOpen(false);
      loadDashboardData();
    } catch (error) {
      console.error('Failed to add payment method:', error);
      toast('Gagal menambahkan metode pembayaran', {
        description: 'Silakan coba lagi nanti',
        type: 'error'
      });
    }
  };

  const formatCurrency = (value: number | undefined | null) => {
    const numValue = Number(value) || 0;
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(numValue);
  };

  // Get current active order
  const currentOrder = orders.find(order =>
    ['pending', 'processing', 'delivering'].includes(order.status)
  );

  // Get completed orders for history
  const orderHistory = orders.filter(order =>
    ['delivered', 'completed'].includes(order.status)
  );

  const addToCart = (menuItem: { id: string; name: string; price: number }, restaurantId: string, restaurantName: string) => {
    setCartItems(prevItems => {
      const existingItem = prevItems.find(item => item.id === menuItem.id);

      if (existingItem) {
        return prevItems.map(item =>
          item.id === menuItem.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        );
      }

      return [...prevItems, {
        id: menuItem.id,
        name: menuItem.name,
        price: menuItem.price,
        quantity: 1,
        restaurantId,
        restaurantName
      }];
    });

    toast({
      title: "Ditambahkan ke keranjang",
      description: `${menuItem.name} telah ditambahkan ke keranjang`,
    });
  };

  const removeFromCart = (itemId: string) => {
    setCartItems(prevItems => prevItems.filter(item => item.id !== itemId));
  };

  const updateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;

    setCartItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId
          ? { ...item, quantity: newQuantity }
          : item
      )
    );
  };

  const getCartTotal = () => {
    return cartItems.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const getDeliveryFee = () => {
    if (cartItems.length === 0) return 0;
    const restaurant = restaurants.find(r => r.id === cartItems[0].restaurantId);
    return restaurant?.deliveryFee || 0;
  };

  const handleCheckout = () => {
    if (cartItems.length === 0) {
      toast({
        title: "Keranjang kosong",
        description: "Tambahkan item ke keranjang terlebih dahulu",
        variant: "destructive",
      });
      return;
    }

    setCheckoutStep('address');
  };

  const handlePlaceOrder = () => {
    toast({
      title: "Pesanan berhasil!",
      description: "Pesanan Anda sedang diproses",
    });
    setCartItems([]);
    setShowCart(false);
    setCheckoutStep('cart');
  };

  const handleOrderNow = (menuItem: any, restaurant: any) => {
    setCartItems([{
      id: menuItem.id,
      name: menuItem.name,
      price: menuItem.price,
      quantity: 1,
      restaurantId: restaurant.id,
      restaurantName: restaurant.name
    }]);
    setShowCart(true);
    setCheckoutStep('address');
    setShowOrderDialog(false);
  };

  const handleAddToCart = (menuItem: any, restaurant: any) => {
    addToCart(menuItem, restaurant.id, restaurant.name);
    setShowOrderDialog(false);
  };

  const handleMarkNotificationAsRead = async (notificationId: string) => {
    try {
      await notificationService.markAsRead(notificationId);
      loadDashboardData();
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
      toast('Gagal menandai notifikasi sebagai telah dibaca', {
        description: 'Silakan coba lagi nanti',
        type: 'error'
      });
    }
  };

  // Promo & Discount functions
  const handleApplyPromo = async () => {
    if (!promoCode.trim()) {
      toast('Masukkan kode promo', {
        description: 'Kode promo tidak boleh kosong',
        type: 'error'
      });
      return;
    }

    try {
      const response = await fetch('http://localhost:5000/api/customer/apply-promo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: promoCode,
          orderTotal: getCartTotal(),
          userId
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Gagal menerapkan promo');
      }

      setAppliedPromo(result.promo);
      setPromoCode('');
      toast('Promo berhasil diterapkan!', {
        description: `Hemat ${formatCurrency(result.promo.totalSavings)}`,
        type: 'success'
      });
    } catch (error) {
      console.error('Failed to apply promo:', error);
      toast('Gagal menerapkan promo', {
        description: error.message,
        type: 'error'
      });
    }
  };

  const handleRemovePromo = () => {
    setAppliedPromo(null);
    toast('Promo dihapus', {
      type: 'success'
    });
  };

  const getDiscountedTotal = () => {
    const subtotal = getCartTotal();
    const deliveryFee = getDeliveryFee();

    if (!appliedPromo) {
      return subtotal + deliveryFee;
    }

    const discountAmount = appliedPromo.discountAmount || 0;
    const deliveryDiscount = appliedPromo.deliveryDiscount || 0;

    return subtotal - discountAmount + deliveryFee - deliveryDiscount;
  };

  // Debug info
  console.log('Customer Dashboard State:', {
    loading,
    error,
    restaurantsCount: restaurants.length,
    promotionsCount: promotions.length,
    userId
  });

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">GoFood Customer</h1>
            <p className="text-gray-600 flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Jl. Sudirman No. 123, Jakarta Pusat
            </p>
            {/* Debug info */}
            <p className="text-xs text-gray-400 mt-1">
              Debug: {restaurants.length} restaurants, {promotions.length} promotions, Loading: {loading ? 'Yes' : 'No'}
            </p>
          </div>
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={loadDashboardData}
              disabled={loading}
              className="flex items-center gap-2"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"></div>
              ) : (
                <span>🔄</span>
              )}
              Refresh
            </Button>
            <Button
              variant="outline"
              className="relative"
              onClick={() => setShowCart(!showCart)}
            >
              <ShoppingCart className="h-5 w-5" />
              {cartItems.length > 0 && (
                <Badge className="absolute -top-2 -right-2 bg-orange-500">
                  {cartItems.reduce((total, item) => total + item.quantity, 0)}
                </Badge>
              )}
            </Button>
            <Button onClick={logout} variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Logout
            </Button>
          </div>
        </div>

        {/* Cart Sidebar */}
        {showCart && (
          <div className="fixed inset-0 bg-black bg-opacity-50 z-50">
            <div className="absolute right-0 top-0 h-full w-full max-w-md bg-white shadow-lg">
              <div className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold">Keranjang</h2>
                  <Button variant="ghost" onClick={() => setShowCart(false)}>
                    ✕
                  </Button>
                </div>

                {checkoutStep === 'cart' && (
                  <>
                    {cartItems.length === 0 ? (
                      <div className="text-center py-8">
                        <ShoppingCart className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                        <p className="text-gray-500">Keranjang Anda kosong</p>
                      </div>
                    ) : (
                      <>
                        <div className="space-y-4 mb-6">
                          {cartItems.map((item) => (
                            <div key={item.id} className="flex items-center justify-between p-4 border rounded-lg">
                              <div>
                                <h3 className="font-medium">{item.name}</h3>
                                <p className="text-sm text-gray-600">{item.restaurantName}</p>
                                <p className="font-medium">{formatCurrency(item.price)}</p>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                >
                                  <Minus className="h-4 w-4" />
                                </Button>
                                <span className="w-8 text-center">{item.quantity}</span>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                >
                                  <Plus className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeFromCart(item.id)}
                                  className="text-red-600"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          ))}
                        </div>

                        <div className="border-t pt-4 space-y-2">
                          <div className="flex justify-between">
                            <span>Subtotal</span>
                            <span>{formatCurrency(getCartTotal())}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Ongkos Kirim</span>
                            <span>{formatCurrency(getDeliveryFee())}</span>
                          </div>
                          <div className="flex justify-between font-bold text-lg">
                            <span>Total</span>
                            <span>{formatCurrency(getCartTotal() + getDeliveryFee())}</span>
                          </div>
                        </div>

                        <Button
                          className="w-full mt-6 bg-orange-500 hover:bg-orange-600"
                          onClick={handleCheckout}
                        >
                          Checkout
                        </Button>
                      </>
                    )}
                  </>
                )}

                {checkoutStep === 'address' && (
                  <div className="space-y-6">
                    <h3 className="font-medium">Alamat Pengiriman</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium mb-1">Alamat</label>
                        <Input defaultValue="Jl. Sudirman No. 123" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Kota</label>
                        <Input defaultValue="Jakarta Pusat" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Kode Pos</label>
                        <Input defaultValue="10220" />
                      </div>
                    </div>
                    <div className="flex gap-4">
                      <Button
                        variant="outline"
                        className="flex-1"
                        onClick={() => setCheckoutStep('cart')}
                      >
                        Kembali
                      </Button>
                      <Button
                        className="flex-1 bg-orange-500 hover:bg-orange-600"
                        onClick={() => setCheckoutStep('payment')}
                      >
                        Lanjut ke Pembayaran
                      </Button>
                    </div>
                  </div>
                )}

                {checkoutStep === 'payment' && (
                  <div className="space-y-6">
                    <h3 className="font-medium">Metode Pembayaran</h3>
                    <div className="space-y-4">
                      <div className="border rounded-lg p-4 cursor-pointer hover:border-orange-500">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 rounded-full border-2 border-orange-500 flex items-center justify-center">
                            <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                          </div>
                          <div>
                            <p className="font-medium">GoPay</p>
                            <p className="text-sm text-gray-600">Bayar dengan saldo GoPay</p>
                          </div>
                        </div>
                      </div>
                      <div className="border rounded-lg p-4 cursor-pointer hover:border-orange-500">
                        <div className="flex items-center gap-3">
                          <div className="w-6 h-6 rounded-full border-2 border-gray-300"></div>
                          <div>
                            <p className="font-medium">Kartu Kredit/Debit</p>
                            <p className="text-sm text-gray-600">Visa, Mastercard, JCB</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="border-t pt-4 space-y-2">
                      <div className="flex justify-between">
                        <span>Subtotal</span>
                        <span>{formatCurrency(getCartTotal())}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Ongkos Kirim</span>
                        <span>{formatCurrency(getDeliveryFee())}</span>
                      </div>
                      <div className="flex justify-between font-bold text-lg">
                        <span>Total</span>
                        <span>{formatCurrency(getCartTotal() + getDeliveryFee())}</span>
                      </div>
                    </div>
                    <div className="flex gap-4">
                      <Button
                        variant="outline"
                        className="flex-1"
                        onClick={() => setCheckoutStep('address')}
                      >
                        Kembali
                      </Button>
                      <Button
                        className="flex-1 bg-orange-500 hover:bg-orange-600"
                        onClick={handlePlaceOrder}
                      >
                        Bayar Sekarang
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Search Bar */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Cari restoran atau makanan..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 h-12 text-lg"
              />
            </div>
          </CardContent>
        </Card>

        <Tabs defaultValue="restaurants" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full max-w-md">
            <TabsTrigger value="restaurants">Restaurants</TabsTrigger>
            <TabsTrigger value="tracking">Tracking</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
            <TabsTrigger value="profile">Profile</TabsTrigger>
          </TabsList>

          <TabsContent value="restaurants" className="space-y-6">
            {/* Special Offers Carousel */}
            <Card>
              <CardHeader>
                <CardTitle>Promo Spesial Hari Ini</CardTitle>
                <CardDescription>Jangan lewatkan penawaran terbaik!</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {promotions.slice(0, 6).map((promo) => (
                    <div
                      key={promo.id}
                      className={`bg-gradient-to-r ${
                        promo.color === 'orange' ? 'from-orange-400 to-orange-600' :
                        promo.color === 'green' ? 'from-green-400 to-green-600' :
                        promo.color === 'red' ? 'from-red-400 to-red-600' :
                        promo.color === 'purple' ? 'from-purple-400 to-purple-600' :
                        promo.color === 'blue' ? 'from-blue-400 to-blue-600' :
                        'from-gray-400 to-gray-600'
                      } text-white p-6 rounded-lg cursor-pointer hover:scale-105 transition-transform`}
                      onClick={() => {
                        setPromoCode(promo.code);
                        setIsPromoDialogOpen(true);
                      }}
                    >
                      <h3 className="text-xl font-bold mb-2">{promo.title}</h3>
                      <p className="mb-4">{promo.description}</p>
                      <div className="flex items-center justify-between">
                        <Badge className={`bg-white ${
                          promo.color === 'orange' ? 'text-orange-600' :
                          promo.color === 'green' ? 'text-green-600' :
                          promo.color === 'red' ? 'text-red-600' :
                          promo.color === 'purple' ? 'text-purple-600' :
                          promo.color === 'blue' ? 'text-blue-600' :
                          'text-gray-600'
                        }`}>
                          Kode: {promo.code}
                        </Badge>
                        <span className="text-sm opacity-90">Tap untuk gunakan</span>
                      </div>
                    </div>
                  ))}
                </div>

                {promotions.length === 0 && (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-4">🎉</div>
                    <h3 className="text-lg font-semibold mb-2">Belum ada promo</h3>
                    <p className="text-gray-600">Promo menarik akan muncul di sini</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Quick Categories */}
            <Card>
              <CardHeader>
                <CardTitle>Kategori Populer</CardTitle>
                <CardDescription>Temukan makanan favorit Anda</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {[
                    { name: 'Makanan Padang', icon: '🍛', count: '12 resto' },
                    { name: 'Fast Food', icon: '🍔', count: '8 resto' },
                    { name: 'Minuman', icon: '🥤', count: '15 resto' },
                    { name: 'Dessert', icon: '🍰', count: '6 resto' }
                  ].map((category, index) => (
                    <div key={index} className="text-center p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                      <div className="text-3xl mb-2">{category.icon}</div>
                      <h3 className="font-semibold text-sm">{category.name}</h3>
                      <p className="text-xs text-gray-500">{category.count}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Trending Menu */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  🔥 Menu Trending
                  <Badge variant="secondary">Hot</Badge>
                </CardTitle>
                <CardDescription>Menu yang paling banyak dipesan hari ini</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {restaurants.flatMap(restaurant =>
                    restaurant.menu.slice(0, 2).map((item, index) => (
                      <div key={`${restaurant.id}-${item.id}`} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between mb-2">
                          <Badge className="bg-red-100 text-red-700 text-xs">#{index + 1} Trending</Badge>
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        </div>
                        <h4 className="font-semibold mb-1">{item.name}</h4>
                        <p className="text-sm text-gray-600 mb-2">{restaurant.name}</p>
                        <div className="flex items-center justify-between">
                          <span className="font-bold text-orange-600">{formatCurrency(item.price)}</span>
                          <Button
                            size="sm"
                            className="bg-orange-500 hover:bg-orange-600"
                            onClick={() => {
                              setSelectedMenuItem(item);
                              setSelectedRestaurant(restaurant);
                              setShowOrderDialog(true);
                            }}
                          >
                            Pesan
                          </Button>
                        </div>
                      </div>
                    ))
                  ).slice(0, 8)}
                </div>
              </CardContent>
            </Card>

            {/* Restaurant List */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Restoran Terdekat</CardTitle>
                    <CardDescription>Pilih restoran favorit Anda</CardDescription>
                  </div>
                  <Badge variant="outline">{restaurants.length} restoran</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {restaurants.map((restaurant) => {
                    const customerPrice = calculateCustomerPrice(restaurant.basePrice);
                    const isFavorite = favorites.some(fav => fav.id === restaurant.id);

                    return (
                      <div key={restaurant.id} className="border rounded-lg overflow-hidden bg-white hover:shadow-lg transition-all duration-300 group">
                        <div className="h-48 bg-gradient-to-br from-orange-200 to-red-200 relative">
                          <div className="absolute inset-0 bg-black opacity-0 group-hover:opacity-10 transition-opacity"></div>
                          {restaurant.specialOffer && (
                            <Badge className="absolute top-2 left-2 bg-red-500">
                              {restaurant.specialOffer}
                            </Badge>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                            onClick={() => isFavorite ?
                              handleRemoveFromFavorites(restaurant.id) :
                              handleAddToFavorites(restaurant.id)
                            }
                          >
                            <Heart className={`h-4 w-4 ${isFavorite ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} />
                          </Button>
                          <div className="absolute bottom-2 left-2 bg-white rounded-lg px-2 py-1">
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 text-yellow-500 fill-current" />
                              <span className="text-sm font-semibold">{restaurant.rating}</span>
                            </div>
                          </div>
                        </div>
                        <div className="p-4">
                          <h3 className="font-semibold text-lg mb-2">{restaurant.name}</h3>
                          <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {restaurant.deliveryTime}
                            </span>
                            <span className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {restaurant.distance}
                            </span>
                          </div>
                          <div className="mb-4">
                            <p className="text-sm text-gray-600">{restaurant.cuisine}</p>
                            <p className="text-sm font-medium">Mulai dari {formatCurrency(customerPrice)}</p>
                            <p className="text-sm text-gray-500">Ongkir: {formatCurrency(restaurant.deliveryFee)}</p>
                          </div>

                          {/* Popular Menu Preview */}
                          <div className="mb-4">
                            <h4 className="font-semibold mb-2 text-sm">Menu Populer:</h4>
                            <div className="space-y-2">
                              {restaurant.menu.slice(0, 3).map((item: any, index) => (
                                <div key={item.id} className="flex items-center justify-between p-2 border rounded hover:bg-gray-50">
                                  <div className="flex-1">
                                    <p className="font-medium text-sm">{item.name}</p>
                                    <p className="text-xs text-gray-600">{formatCurrency(item.price)}</p>
                                  </div>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    className="text-xs px-2 py-1 h-auto"
                                    onClick={() => {
                                      setSelectedMenuItem(item);
                                      setSelectedRestaurant(restaurant);
                                      setShowOrderDialog(true);
                                    }}
                                  >
                                    +
                                  </Button>
                                </div>
                              ))}
                            </div>
                          </div>

                          <Button
                            className="w-full bg-orange-500 hover:bg-orange-600"
                            onClick={() => {
                              // Show all menu items for this restaurant
                              setSelectedRestaurant(restaurant);
                              // Could open a detailed restaurant view
                            }}
                          >
                            Lihat Semua Menu ({restaurant.menu.length})
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>

                {loading && (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
                    <h3 className="text-xl font-semibold mb-2">Memuat restoran...</h3>
                    <p className="text-gray-600">Mohon tunggu sebentar</p>
                  </div>
                )}

                {!loading && error && (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">⚠️</div>
                    <h3 className="text-xl font-semibold mb-2 text-red-600">Terjadi Kesalahan</h3>
                    <p className="text-gray-600 mb-4">{error}</p>
                    <Button onClick={loadDashboardData} className="bg-orange-500 hover:bg-orange-600">
                      Coba Lagi
                    </Button>
                  </div>
                )}

                {!loading && !error && restaurants.length === 0 && (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🍽️</div>
                    <h3 className="text-xl font-semibold mb-2">Belum ada restoran</h3>
                    <p className="text-gray-600">Restoran akan muncul di sini setelah tersedia</p>
                    <Button onClick={loadDashboardData} variant="outline" className="mt-4">
                      Refresh
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="tracking" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Lacak Pesanan
                </CardTitle>
                <CardDescription>Status pesanan Anda saat ini</CardDescription>
              </CardHeader>
              <CardContent>
                {currentOrder ? (
                  <div className="space-y-6">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div>
                          <h3 className="font-semibold text-lg">{currentOrder.business_name || currentOrder.merchant_name}</h3>
                          <p className="text-gray-600">Order ID: {currentOrder.id}</p>
                        </div>
                        <Badge className="bg-blue-500">
                          {currentOrder.status === 'pending' ? 'Menunggu Konfirmasi' :
                           currentOrder.status === 'processing' ? 'Sedang Diproses' :
                           currentOrder.status === 'delivering' ? 'Sedang Diantar' : 'Selesai'}
                        </Badge>
                      </div>

                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-medium mb-2">Detail Pesanan</h4>
                          <p className="text-sm text-gray-600 mb-1">{currentOrder.items || 'Detail pesanan'}</p>
                          <p className="font-medium">{formatCurrency(currentOrder.total_amount)}</p>
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">Driver</h4>
                          <p className="text-sm text-gray-600 mb-1">{currentOrder.driver_name || 'Belum ada driver'}</p>
                          <p className="text-sm font-medium text-green-600">
                            Estimasi tiba: {currentOrder.estimatedTime || '30-45 menit'}
                          </p>
                        </div>
                      </div>

                      <div className="mt-6 flex gap-3">
                        <Button variant="outline" className="flex items-center gap-2">
                          <MessageSquare className="h-4 w-4" />
                          Chat Driver
                        </Button>
                        <Button variant="outline" className="flex items-center gap-2">
                          <MapPin className="h-4 w-4" />
                          Lihat di Map
                        </Button>
                      </div>
                    </div>

                    {/* Order Status Timeline */}
                    <div className="border rounded-lg p-6">
                      <h4 className="font-medium mb-4">Status Pesanan</h4>
                      <div className="space-y-4">
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm">Pesanan dikonfirmasi</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm">Makanan sedang dipersiapkan</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span className="text-sm">Driver mengambil pesanan</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-blue-500 rounded-full animate-pulse"></div>
                          <span className="text-sm font-medium">Sedang dalam perjalanan</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                          <span className="text-sm text-gray-500">Pesanan tiba</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <p className="text-xl text-gray-500 mb-4">Tidak ada pesanan aktif</p>
                    <p className="text-gray-400">Pesan makanan dari restoran favorit Anda</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Riwayat Pesanan</CardTitle>
                <CardDescription>Pesanan yang telah Anda buat sebelumnya</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {orderHistory.length > 0 ? (
                    orderHistory.map((order) => (
                      <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h3 className="font-medium">{order.business_name || order.merchant_name}</h3>
                          <p className="text-sm text-gray-600">{new Date(order.created_at).toLocaleDateString('id-ID')}</p>
                          <p className="text-sm">Order ID: {order.id}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">{formatCurrency(order.total_amount)}</p>
                          {order.rating && (
                            <div className="flex items-center gap-1 mt-1">
                              <span className="text-sm">Rating:</span>
                              <span className="text-sm">{'⭐'.repeat(order.rating)}</span>
                            </div>
                          )}
                          <Badge variant="secondary" className="mt-1">
                            {order.status === 'delivered' ? 'Selesai' : 'Completed'}
                          </Badge>
                          {!order.rating && (
                            <Button
                              size="sm"
                              variant="outline"
                              className="mt-2"
                              onClick={() => {
                                setSelectedOrder(order);
                                setIsReviewDialogOpen(true);
                              }}
                            >
                              Beri Rating
                            </Button>
                          )}
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500">Belum ada riwayat pesanan</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profile" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Profil Saya
                </CardTitle>
                <CardDescription>Kelola informasi akun Anda</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium mb-4">Informasi Pribadi</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium mb-1">Nama</label>
                        <Input defaultValue={user?.name || 'Customer'} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Email</label>
                        <Input defaultValue={user?.email || '<EMAIL>'} />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Telepon</label>
                        <Input defaultValue={user?.phone || '+62 812 3456 789'} />
                      </div>
                    </div>
                  </div>
                  <div>
                    <h3 className="font-medium mb-4">Alamat Pengiriman</h3>
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium mb-1">Alamat Utama</label>
                        <Input defaultValue="Jl. Sudirman No. 123" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Kota</label>
                        <Input defaultValue="Jakarta Pusat" />
                      </div>
                      <div>
                        <label className="block text-sm font-medium mb-1">Kode Pos</label>
                        <Input defaultValue="10220" />
                      </div>
                    </div>
                  </div>
                </div>
                <Button className="bg-orange-500 hover:bg-orange-600">
                  Simpan Perubahan
                </Button>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Order Dialog */}
      <Dialog open={showOrderDialog} onOpenChange={setShowOrderDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Pilih Opsi Pesanan</DialogTitle>
            <DialogDescription>
              {selectedMenuItem && (
                <div className="mt-4">
                  <p className="font-medium">{selectedMenuItem.name}</p>
                  <p className="text-sm text-gray-600">{formatCurrency(selectedMenuItem.price)}</p>
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-2 gap-4 mt-4">
            <Button
              variant="outline"
              className="flex items-center justify-center gap-2"
              onClick={() => selectedMenuItem && selectedRestaurant && handleAddToCart(selectedMenuItem, selectedRestaurant)}
            >
              <ShoppingCart className="h-4 w-4" />
              Masukkan Keranjang
            </Button>
            <Button
              className="bg-orange-500 hover:bg-orange-600 flex items-center justify-center gap-2"
              onClick={() => selectedMenuItem && selectedRestaurant && handleOrderNow(selectedMenuItem, selectedRestaurant)}
            >
              Pesan Sekarang
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <ReviewDialog
        open={isReviewDialogOpen}
        onOpenChange={setIsReviewDialogOpen}
        order={selectedOrder}
        onSave={handleAddReview}
      />

      <PaymentMethodDialog
        open={isPaymentMethodDialogOpen}
        onOpenChange={setIsPaymentMethodDialogOpen}
        onSave={handleAddPaymentMethod}
      />
    </div>
  );
};

export default CustomerDashboard;
