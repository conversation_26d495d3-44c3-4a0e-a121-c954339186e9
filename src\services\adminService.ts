import axios from 'axios';

interface DashboardStats {
  totalUsers: number;
  totalMerchants: number;
  totalDrivers: number;
  totalTransactions: number;
  totalRevenue: number;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
  created_at: string;
}

interface Transaction {
  id: string;
  user_id: string;
  merchant_id: string;
  driver_id: string | null;
  total_amount: number;
  status: string;
  created_at: string;
}

interface ActivityLog {
  id: string;
  user_id: string;
  action: string;
  description: string;
  created_at: string;
}

export interface SystemSettings {
  id?: number;
  price_markup_percentage: number;
  delivery_base_rate: number;
  merchant_commission_percentage: number;
  driver_commission_percentage: number;
  updated_at?: string;
}

const API_URL = 'http://localhost:5000/api';

export const adminService = {
  // Dashboard Stats
  getDashboardStats: async (): Promise<DashboardStats> => {
    try {
      const response = await axios.get(`${API_URL}/admin/stats`);
      return response.data;
    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw error;
    }
  },

  // Users Management
  getPendingUsers: async (): Promise<User[]> => {
    try {
      const response = await axios.get(`${API_URL}/admin/pending-users`);
      return response.data;
    } catch (error) {
      console.error('Error fetching pending users:', error);
      throw error;
    }
  },

  getUsers: async (search?: string, role?: string): Promise<User[]> => {
    try {
      const response = await axios.get(`${API_URL}/admin/users`, {
        params: { search, role }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  },

  // Transactions
  getRecentTransactions: async (): Promise<Transaction[]> => {
    try {
      const response = await axios.get(`${API_URL}/admin/transactions`);
      return response.data;
    } catch (error) {
      console.error('Error fetching transactions:', error);
      throw error;
    }
  },

  // Activity Logs
  getActivityLogs: async (): Promise<ActivityLog[]> => {
    try {
      const response = await axios.get(`${API_URL}/admin/activity-logs`);
      return response.data;
    } catch (error) {
      console.error('Error fetching activity logs:', error);
      throw error;
    }
  },

  // System Settings
  getSystemSettings: async (): Promise<SystemSettings> => {
    try {
      const response = await axios.get(`${API_URL}/admin/system-settings`);
      return response.data;
    } catch (error) {
      console.error('Error fetching system settings:', error);
      throw error;
    }
  },

  updateSystemSettings: async (settings: SystemSettings): Promise<SystemSettings> => {
    try {
      console.log('Sending update request:', settings);
      const response = await axios.put(`${API_URL}/admin/system-settings`, settings);
      console.log('Update response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating system settings:', error);
      throw error;
    }
  },

  // User Management Actions
  approveUser: async (userId: string): Promise<void> => {
    try {
      await axios.post(`${API_URL}/admin/approve-user/${userId}`);
    } catch (error) {
      console.error('Error approving user:', error);
      throw error;
    }
  },

  suspendUser: async (userId: string): Promise<void> => {
    try {
      await axios.post(`${API_URL}/admin/suspend-user/${userId}`);
    } catch (error) {
      console.error('Error suspending user:', error);
      throw error;
    }
  },

  deleteUser: async (userId: string): Promise<void> => {
    try {
      await axios.delete(`${API_URL}/admin/users/${userId}`);
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }
};