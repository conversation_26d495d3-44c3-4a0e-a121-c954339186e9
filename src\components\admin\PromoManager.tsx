
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Plus, Edit, Trash, Gift, Users, Truck, Store } from 'lucide-react';
import { Promo } from '@/types';

const PromoManager = () => {
  const { promos, createPromo, updatePromo, deletePromo } = useAuth();
  const { toast } = useToast();
  const [showDialog, setShowDialog] = useState(false);
  const [editingPromo, setEditingPromo] = useState<Promo | null>(null);

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'delivery_discount' as const,
    value: 0,
    minOrder: 0,
    startDate: '',
    endDate: '',
    targetRole: 'all' as const,
    isActive: true,
    usageLimit: 0,
  });

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      type: 'delivery_discount',
      value: 0,
      minOrder: 0,
      startDate: '',
      endDate: '',
      targetRole: 'all',
      isActive: true,
      usageLimit: 0,
    });
    setEditingPromo(null);
  };

  const handleEdit = (promo: Promo) => {
    setFormData({
      title: promo.title,
      description: promo.description,
      type: promo.type,
      value: promo.value,
      minOrder: promo.minOrder,
      startDate: promo.startDate.toISOString().split('T')[0],
      endDate: promo.endDate.toISOString().split('T')[0],
      targetRole: promo.targetRole,
      isActive: promo.isActive,
      usageLimit: promo.usageLimit || 0,
    });
    setEditingPromo(promo);
    setShowDialog(true);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const promoData = {
      title: formData.title,
      description: formData.description,
      type: formData.type,
      value: formData.value,
      minOrder: formData.minOrder,
      startDate: new Date(formData.startDate),
      endDate: new Date(formData.endDate),
      targetRole: formData.targetRole,
      isActive: formData.isActive,
      usageLimit: formData.usageLimit || undefined,
    };

    if (editingPromo) {
      updatePromo(editingPromo.id, promoData);
      toast({
        title: "Promo Diperbarui",
        description: "Promo berhasil diperbarui.",
      });
    } else {
      createPromo(promoData);
      toast({
        title: "Promo Dibuat",
        description: "Promo baru berhasil dibuat.",
      });
    }

    setShowDialog(false);
    resetForm();
  };

  const handleDelete = (promoId: string, title: string) => {
    deletePromo(promoId);
    toast({
      title: "Promo Dihapus",
      description: `Promo "${title}" berhasil dihapus.`,
      variant: "destructive",
    });
  };

  const getPromoTypeIcon = (type: Promo['type']) => {
    switch (type) {
      case 'delivery_discount':
        return <Truck className="h-4 w-4" />;
      case 'cashback':
        return <Gift className="h-4 w-4" />;
      case 'free_delivery':
        return <Truck className="h-4 w-4" />;
      default:
        return <Gift className="h-4 w-4" />;
    }
  };

  const getTargetRoleIcon = (role: Promo['targetRole']) => {
    switch (role) {
      case 'customer':
        return <Users className="h-4 w-4" />;
      case 'merchant':
        return <Store className="h-4 w-4" />;
      case 'driver':
        return <Truck className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const formatCurrency = (value: number | undefined | null) => {
    const numValue = Number(value) || 0;
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(numValue);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Gift className="h-5 w-5" />
              Manajemen Promo
            </CardTitle>
            <CardDescription>
              Kelola promo untuk semua pengguna aplikasi
            </CardDescription>
          </div>
          <Dialog open={showDialog} onOpenChange={setShowDialog}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Buat Promo
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingPromo ? 'Edit Promo' : 'Buat Promo Baru'}
                </DialogTitle>
                <DialogDescription>
                  Buat promo untuk meningkatkan engagement pengguna
                </DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="title">Judul Promo</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => setFormData({...formData, title: e.target.value})}
                    placeholder="Contoh: Gratis Ongkir untuk Semua"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="Jelaskan detail promo"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="type">Tipe Promo</Label>
                    <Select value={formData.type} onValueChange={(value) => setFormData({...formData, type: value as Promo['type']})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="delivery_discount">Diskon Ongkir</SelectItem>
                        <SelectItem value="cashback">Cashback</SelectItem>
                        <SelectItem value="free_delivery">Gratis Ongkir</SelectItem>
                        <SelectItem value="buy_one_get_one">Buy One Get One</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="targetRole">Target Pengguna</Label>
                    <Select value={formData.targetRole} onValueChange={(value) => setFormData({...formData, targetRole: value as Promo['targetRole']})}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua Pengguna</SelectItem>
                        <SelectItem value="customer">Customer</SelectItem>
                        <SelectItem value="merchant">Merchant</SelectItem>
                        <SelectItem value="driver">Driver</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="value">Nilai Promo</Label>
                    <Input
                      id="value"
                      type="number"
                      value={formData.value}
                      onChange={(e) => setFormData({...formData, value: Number(e.target.value)})}
                      placeholder="10000"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="minOrder">Min. Order (Rp)</Label>
                    <Input
                      id="minOrder"
                      type="number"
                      value={formData.minOrder}
                      onChange={(e) => setFormData({...formData, minOrder: Number(e.target.value)})}
                      placeholder="50000"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate">Tanggal Mulai</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => setFormData({...formData, startDate: e.target.value})}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="endDate">Tanggal Berakhir</Label>
                    <Input
                      id="endDate"
                      type="date"
                      value={formData.endDate}
                      onChange={(e) => setFormData({...formData, endDate: e.target.value})}
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="usageLimit">Batas Penggunaan (opsional)</Label>
                  <Input
                    id="usageLimit"
                    type="number"
                    value={formData.usageLimit}
                    onChange={(e) => setFormData({...formData, usageLimit: Number(e.target.value)})}
                    placeholder="1000"
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData({...formData, isActive: checked})}
                  />
                  <Label htmlFor="isActive">Aktifkan promo</Label>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button type="button" variant="outline" onClick={() => setShowDialog(false)} className="flex-1">
                    Batal
                  </Button>
                  <Button type="submit" className="flex-1">
                    {editingPromo ? 'Update' : 'Buat'} Promo
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {promos.length === 0 ? (
          <div className="text-center py-8">
            <Gift className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Belum ada promo dibuat</p>
            <p className="text-gray-400 text-sm">Buat promo pertama untuk menarik pengguna</p>
          </div>
        ) : (
          <div className="space-y-4">
            {promos.map((promo) => (
              <div key={promo.id} className="border rounded-lg p-4 bg-white">
                <div className="flex justify-between items-start mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      {getPromoTypeIcon(promo.type)}
                      <h3 className="font-semibold">{promo.title}</h3>
                      {!promo.isActive && (
                        <Badge variant="secondary">Nonaktif</Badge>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{promo.description}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        {getTargetRoleIcon(promo.targetRole)}
                        <span>{promo.targetRole === 'all' ? 'Semua' : promo.targetRole}</span>
                      </div>
                      <span>Nilai: {formatCurrency(promo.value)}</span>
                      <span>Min: {formatCurrency(promo.minOrder)}</span>
                      <span>Digunakan: {promo.usedCount}/{promo.usageLimit || '∞'}</span>
                    </div>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Button size="sm" variant="outline" onClick={() => handleEdit(promo)}>
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDelete(promo.id, promo.title)}
                    >
                      <Trash className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <div className="text-xs text-gray-400">
                  {promo.startDate.toLocaleDateString()} - {promo.endDate.toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PromoManager;
