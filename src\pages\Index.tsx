
import React, { useState } from 'react';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import AuthPage from '@/components/auth/AuthPage';
import AdminDashboard from '@/components/admin/AdminDashboard';
import MerchantDashboard from '@/components/merchant/MerchantDashboard';
import DriverDashboard from '@/components/driver/DriverDashboard';
import CustomerDashboard from '@/components/customer/CustomerDashboard';
import LandingPage from '@/components/LandingPage';

const AppContent = () => {
  const { user } = useAuth();
  const [currentPage, setCurrentPage] = useState<'landing' | 'auth' | 'dashboard'>('landing');

  // Handle page navigation
  const navigateToAuth = () => setCurrentPage('auth');
  const navigateToLanding = () => setCurrentPage('landing');

  // Show landing page if no user and on landing page
  if (!user && currentPage === 'landing') {
    return <LandingPage onNavigateToAuth={navigateToAuth} />;
  }

  // Show auth page if no user and on auth page
  if (!user) {
    return <AuthPage onNavigateToLanding={navigateToLanding} />;
  }

  // Show dashboard based on user role
  switch (user.role) {
    case 'admin':
      return <AdminDashboard />;
    case 'merchant':
      return <MerchantDashboard />;
    case 'driver':
      return <DriverDashboard />;
    case 'customer':
      return <CustomerDashboard />;
    default:
      return <AuthPage onNavigateToLanding={navigateToLanding} />;
  }
};

const Index = () => {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default Index;
