
import React, { useState } from 'react';
import { AuthProvider, useAuth } from '@/contexts/AuthContext';
import AuthPage from '@/components/auth/AuthPage';
import AdminDashboard from '@/components/admin/AdminDashboard';
import MerchantDashboard from '@/components/merchant/MerchantDashboard';
import DriverDashboard from '@/components/driver/DriverDashboard';
import CustomerDashboard from '@/components/customer/CustomerDashboard';
import LandingPage from '@/components/LandingPage';

const AppContent = () => {
  const { user } = useAuth();
  const [showAuth, setShowAuth] = useState(false);

  // Show landing page if no user and not showing auth
  if (!user && !showAuth) {
    return <LandingPage />;
  }

  // Show auth page if no user but auth is requested
  if (!user) {
    return <AuthPage />;
  }

  // Show dashboard based on user role
  switch (user.role) {
    case 'admin':
      return <AdminDashboard />;
    case 'merchant':
      return <MerchantDashboard />;
    case 'driver':
      return <DriverDashboard />;
    case 'customer':
      return <CustomerDashboard />;
    default:
      return <AuthPage />;
  }
};

const Index = () => {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
};

export default Index;
