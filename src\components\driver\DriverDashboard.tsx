import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, MapPin, MessageSquare, Navigation, Phone, Clock } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { driverService } from '../../services/driverService';
import { notificationService } from '../../services/notificationService';
import { OrderList } from './OrderList';
import { Map } from './Map';
import { toast } from 'sonner';

const DriverDashboard = () => {
  const { logout, user } = useAuth();
  const { toast: useToastToast } = useToast();
  const [isOnline, setIsOnline] = useState(false);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [estimatedTime, setEstimatedTime] = useState<string>('');
  const [locationError, setLocationError] = useState<string>('');
  const [availableOrders, setAvailableOrders] = useState([]);
  const [activeOrders, setActiveOrders] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [driverStats, setDriverStats] = useState({
    todayDeliveries: 0,
    todayEarnings: 0,
    avgRating: 4.8,
    totalDistance: 0
  });
  const [previousOrderCount, setPreviousOrderCount] = useState(0);
  const [showNewOrderAlert, setShowNewOrderAlert] = useState(false);

  const driverId = user?.id || 'driver-1'; // Use real driver ID from auth

  // Audio notification untuk pesanan baru
  const playNotificationSound = () => {
    try {
      const audio = new Audio('/notification-sound.mp3');
      audio.play().catch(e => console.log('Could not play notification sound:', e));
    } catch (e) {
      console.log('Audio notification not available');
    }
  };

  useEffect(() => {
    loadDashboardData();
    startLocationTracking();

    // Auto-refresh untuk pesanan baru setiap 15 detik jika online
    const interval = setInterval(() => {
      if (isOnline) {
        console.log('Auto-refreshing driver data...');
        loadDashboardData();
      }
    }, 15000);

    return () => clearInterval(interval);
  }, [isOnline]);

  const loadDashboardData = async () => {
    try {
      console.log('Loading driver dashboard data for:', driverId);

      const [availableData, activeData, notificationData] = await Promise.all([
        driverService.getAvailableOrders(),
        driverService.getActiveOrders(driverId),
        notificationService.getNotifications(driverId)
      ]);

      // Deteksi pesanan baru
      if (isOnline && availableData.length > previousOrderCount && previousOrderCount > 0) {
        playNotificationSound();
        setShowNewOrderAlert(true);
        toast.success(`🔔 Ada ${availableData.length - previousOrderCount} pesanan baru!`, {
          duration: 5000,
        });

        // Hide alert after 10 seconds
        setTimeout(() => setShowNewOrderAlert(false), 10000);
      }

      setAvailableOrders(availableData);
      setActiveOrders(activeData);
      setNotifications(notificationData);
      setPreviousOrderCount(availableData.length);

      // Calculate driver stats from real data
      const todayDeliveries = activeData.filter(order =>
        order.status === 'delivered' &&
        new Date(order.updated_at).toDateString() === new Date().toDateString()
      ).length;

      const todayEarnings = activeData
        .filter(order =>
          order.status === 'delivered' &&
          new Date(order.updated_at).toDateString() === new Date().toDateString()
        )
        .reduce((sum, order) => sum + (order.delivery_fee || 8000), 0);

      setDriverStats({
        todayDeliveries,
        todayEarnings,
        avgRating: 4.8,
        totalDistance: todayDeliveries * 2.5 // Estimate 2.5km per delivery
      });

      // Get unread count
      const count = await notificationService.getUnreadCount(driverId);
      setUnreadCount(count);

      console.log('Driver dashboard data loaded:', {
        availableOrders: availableData.length,
        activeOrders: activeData.length,
        notifications: notificationData.length,
        todayDeliveries,
        todayEarnings
      });
    } catch (error) {
      console.error('Error loading driver dashboard data:', error);
      toast.error('Gagal memuat data dashboard');
    }
  };

  const startLocationTracking = () => {
    if ('geolocation' in navigator) {
      navigator.geolocation.watchPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          setCurrentLocation({ latitude, longitude });

          if (isOnline) {
            try {
              await driverService.updateLocation(driverId, {
                latitude,
                longitude,
                is_online: true
              });
            } catch (error) {
              console.error('Failed to update location:', error);
            }
          }
        },
        (error) => {
          console.error('Error getting location:', error);
          useToastToast.error('Failed to get location');
        },
        {
          enableHighAccuracy: true,
          maximumAge: 0,
          timeout: 5000
        }
      );
    }
  };

  const handleAcceptOrder = async (order) => {
    try {
      console.log('Accepting order:', order.id);
      await driverService.acceptOrder(order.id, driverId);

      toast.success(`Pesanan #${order.id} berhasil diterima!`);

      // Reload data to get updated lists
      loadDashboardData();
    } catch (error) {
      console.error('Error accepting order:', error);
      toast.error('Gagal menerima pesanan');
    }
  };

  const handleUpdateOrderStatus = async (orderId, status) => {
    try {
      console.log('Updating order status:', { orderId, status });

      const response = await fetch('http://localhost:5000/api/driver/update-order-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          status,
          driverId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update order status');
      }

      const statusText = status === 'delivered' ? 'Selesai' :
                        status === 'delivering' ? 'Sedang Diantar' : status;

      toast.success(`Status pesanan #${orderId} berhasil diupdate ke ${statusText}`);

      // Reload data to get updated lists
      loadDashboardData();
    } catch (error) {
      console.error('Error updating order status:', error);
      toast.error('Gagal mengupdate status pesanan');
    }
  };

  const handleUpdateOrderTracking = async (orderId, data) => {
    try {
      await driverService.updateOrderTracking(orderId, {
        ...data,
        latitude: currentLocation?.latitude,
        longitude: currentLocation?.longitude
      });
      toast.success('Tracking pesanan berhasil diupdate');
    } catch (error) {
      toast.error('Gagal mengupdate tracking pesanan');
    }
  };

  // Navigation and communication handlers
  const handleCallCustomer = (phone) => {
    if (phone) {
      window.open(`tel:${phone}`, '_self');
      toast.success(`Menghubungi ${phone}`);
    } else {
      toast.error('Nomor telepon tidak tersedia');
    }
  };

  const handleCallMerchant = (phone) => {
    if (phone) {
      window.open(`tel:${phone}`, '_self');
      toast.success(`Menghubungi restoran ${phone}`);
    } else {
      toast.error('Nomor telepon restoran tidak tersedia');
    }
  };

  const handleNavigateToLocation = (address) => {
    if (address) {
      const encodedAddress = encodeURIComponent(address);
      window.open(`https://www.google.com/maps/dir/?api=1&destination=${encodedAddress}`, '_blank');
      toast.success('Membuka navigasi ke lokasi');
    } else {
      toast.error('Alamat tidak tersedia');
    }
  };

  const handleNavigateToMerchant = (order) => {
    const address = order.merchant_address || 'Restaurant Address';
    handleNavigateToLocation(address);
  };

  const handleNavigateToCustomer = (order) => {
    const address = order.customer_address || order.delivery_address || 'Customer Address';
    handleNavigateToLocation(address);
  };

  // Toggle online/offline status
  const handleToggleOnline = () => {
    const newStatus = !isOnline;
    setIsOnline(newStatus);

    if (newStatus) {
      toast.success('🟢 Anda sekarang ONLINE! Siap menerima pesanan');
      // Start location tracking when going online
      startLocationTracking();
      // Load fresh data
      loadDashboardData();
    } else {
      toast.success('🔴 Anda sekarang OFFLINE. Tidak akan menerima pesanan baru');
      // Stop auto-refresh when offline
      setPreviousOrderCount(0);
    }

    console.log('Driver status changed to:', newStatus ? 'ONLINE' : 'OFFLINE');
  };

  const calculateETA = (coords: GeolocationCoordinates) => {
    // Simulasi perhitungan ETA (dalam implementasi nyata, gunakan API routing)
    const randomMinutes = Math.floor(Math.random() * 10) + 5;
    setEstimatedTime(`${randomMinutes} menit`);
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('id-ID', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Driver Dashboard</h1>
            <div className="flex items-center gap-3">
              <p className="text-gray-600">{user?.name || 'Driver Budi'} - Motor Honda Beat</p>
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                isOnline
                  ? 'bg-green-100 text-green-800 border border-green-200'
                  : 'bg-red-100 text-red-800 border border-red-200'
              }`}>
                {isOnline ? '● SIAP MENERIMA PESANAN' : '● TIDAK MENERIMA PESANAN'}
              </div>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium">Status Driver:</span>
              <Button
                onClick={handleToggleOnline}
                variant={isOnline ? "default" : "outline"}
                className={`
                  transition-all duration-300 font-medium px-6 py-2
                  ${isOnline
                    ? "bg-green-600 hover:bg-green-700 text-white shadow-lg"
                    : "border-red-300 text-red-600 hover:bg-red-50"
                  }
                `}
              >
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${isOnline ? 'bg-white' : 'bg-red-500'} animate-pulse`}></div>
                  {isOnline ? '🟢 ONLINE' : '🔴 OFFLINE'}
                </div>
              </Button>
            </div>
            <Button onClick={logout} variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Kembali
            </Button>
          </div>
        </div>

        {/* New Order Alert */}
        {showNewOrderAlert && (
          <div className="mb-6 bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg shadow-lg animate-pulse">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-white rounded-full animate-ping"></div>
                <div>
                  <h3 className="font-bold text-lg">🔔 PESANAN BARU TERSEDIA!</h3>
                  <p className="text-green-100">Ada pesanan baru yang menunggu untuk diambil</p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="bg-white text-green-600 hover:bg-gray-100"
                onClick={() => setShowNewOrderAlert(false)}
              >
                Tutup
              </Button>
            </div>
          </div>
        )}

        {/* Driver Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-green-100">Delivery Hari Ini</p>
                <p className="text-3xl font-bold">{driverStats.todayDeliveries}</p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-blue-100">Penghasilan Hari Ini</p>
                <p className="text-3xl font-bold">
                  {new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                  }).format(driverStats.todayEarnings)}
                </p>
                <p className="text-sm text-blue-100">
                  Target: Rp 150K ({Math.round((driverStats.todayEarnings / 150000) * 100)}%)
                </p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-orange-100">Rating</p>
                <p className="text-3xl font-bold">{driverStats.avgRating}</p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-purple-100">Jarak Tempuh</p>
                <p className="text-3xl font-bold">{driverStats.totalDistance.toFixed(1)} km</p>
                <p className="text-sm text-purple-100">Hari ini</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="orders" className="space-y-6">
          <TabsList className="grid grid-cols-6 w-full max-w-2xl">
            <TabsTrigger value="orders">Pesanan</TabsTrigger>
            <TabsTrigger value="active">Aktif</TabsTrigger>
            <TabsTrigger value="earnings">Pendapatan</TabsTrigger>
            <TabsTrigger value="location">Lokasi</TabsTrigger>
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="notifications">
              Notifikasi
              {unreadCount > 0 && (
                <span className="ml-2 bg-red-500 text-white rounded-full px-2 py-1 text-xs">
                  {unreadCount}
                </span>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="orders" className="space-y-6">
            {!isOnline ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <p className="text-xl text-gray-500 mb-4">Anda sedang offline</p>
                  <p className="text-gray-400">Aktifkan status online untuk melihat pesanan</p>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Pesanan Tersedia
                  </CardTitle>
                  <CardDescription>Pilih pesanan yang ingin Anda ambil</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <OrderList
                      orders={availableOrders}
                      onAccept={handleAcceptOrder}
                      onCall={handleCallMerchant}
                      onNavigate={handleNavigateToMerchant}
                      showAcceptButton
                    />
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="active" className="space-y-6">
            {activeOrders.length > 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle>Pesanan Aktif</CardTitle>
                  <CardDescription>
                    Pilih pesanan untuk mengupdate status
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <OrderList
                    orders={activeOrders}
                    onUpdateStatus={handleUpdateOrderStatus}
                    onUpdateTracking={handleUpdateOrderTracking}
                    onCall={handleCallCustomer}
                    onNavigate={handleNavigateToCustomer}
                    showTracking
                  />
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-12 text-center">
                  <p className="text-xl text-gray-500 mb-4">Tidak ada pesanan aktif</p>
                  <p className="text-gray-400">Ambil pesanan dari tab "Pesanan" untuk memulai</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="earnings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  💰 Detail Pendapatan
                </CardTitle>
                <CardDescription>Ringkasan penghasilan dan target harian</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Daily Progress */}
                  <div className="bg-gradient-to-r from-green-50 to-blue-50 p-6 rounded-lg">
                    <h3 className="font-medium mb-4">Progress Hari Ini</h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Target Penghasilan</span>
                          <span>Rp 150,000</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-green-500 h-3 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min((driverStats.todayEarnings / 150000) * 100, 100)}%` }}
                          ></div>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          {new Intl.NumberFormat('id-ID', {
                            style: 'currency',
                            currency: 'IDR',
                            minimumFractionDigits: 0,
                          }).format(driverStats.todayEarnings)} dari target
                        </p>
                      </div>

                      <div>
                        <div className="flex justify-between text-sm mb-2">
                          <span>Target Delivery</span>
                          <span>20 pesanan</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-blue-500 h-3 rounded-full transition-all duration-300"
                            style={{ width: `${Math.min((driverStats.todayDeliveries / 20) * 100, 100)}%` }}
                          ></div>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          {driverStats.todayDeliveries} dari 20 target delivery
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Earnings Breakdown */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium mb-3">💵 Pendapatan Hari Ini</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Ongkos Kirim ({driverStats.todayDeliveries}x)</span>
                          <span>{new Intl.NumberFormat('id-ID', {
                            style: 'currency',
                            currency: 'IDR',
                            minimumFractionDigits: 0,
                          }).format(driverStats.todayEarnings)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Bonus</span>
                          <span>Rp 0</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Tips</span>
                          <span>Rp 0</span>
                        </div>
                        <div className="border-t pt-2 flex justify-between font-medium">
                          <span>Total</span>
                          <span>{new Intl.NumberFormat('id-ID', {
                            style: 'currency',
                            currency: 'IDR',
                            minimumFractionDigits: 0,
                          }).format(driverStats.todayEarnings)}</span>
                        </div>
                      </div>
                    </div>

                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium mb-3">📊 Statistik</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Rata-rata per delivery</span>
                          <span>
                            {driverStats.todayDeliveries > 0
                              ? new Intl.NumberFormat('id-ID', {
                                  style: 'currency',
                                  currency: 'IDR',
                                  minimumFractionDigits: 0,
                                }).format(driverStats.todayEarnings / driverStats.todayDeliveries)
                              : 'Rp 0'
                            }
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span>Jarak tempuh</span>
                          <span>{driverStats.totalDistance.toFixed(1)} km</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Waktu online</span>
                          <span>{isOnline ? 'Sedang online' : 'Offline'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Rating</span>
                          <span>⭐ {driverStats.avgRating}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="location" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Live Location Tracking
                </CardTitle>
                <CardDescription>Lokasi Anda saat ini</CardDescription>
              </CardHeader>
              <CardContent>
                {locationError ? (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                    <p className="text-red-600">{locationError}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="bg-gray-100 rounded-lg p-8 text-center">
                      <MapPin className="h-12 w-12 mx-auto mb-4 text-blue-500" />
                      <p className="text-lg font-medium mb-2">Lokasi Saat Ini</p>
                      <p className="text-gray-600">
                        Lat: {currentLocation?.latitude.toFixed(6)},
                        Lng: {currentLocation?.longitude.toFixed(6)}
                      </p>
                    </div>
                    <div className="text-center">
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Buka Google Maps dengan lokasi saat ini
                          window.open(
                            `https://www.google.com/maps?q=${currentLocation?.latitude},${currentLocation?.longitude}`,
                            '_blank'
                          );
                        }}
                      >
                        Buka di Google Maps
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="chat" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Chat dengan Customer
                </CardTitle>
              </CardHeader>
              <CardContent>
                {activeOrders.length > 0 ? (
                  <div className="space-y-4">
                    <div className="border rounded-lg p-4 bg-gray-50 h-[400px] overflow-y-auto">
                      <p className="text-sm text-gray-600 mb-4">Chat dengan {activeOrders[0].customer}</p>
                      <div className="space-y-4">
                        {/* Chat messages will be populated here */}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        placeholder="Ketik pesan..."
                        className="flex-1 p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <Button>
                        Kirim
                      </Button>
                    </div>
                  </div>
                ) : (
                  <p className="text-center text-gray-500 py-8">
                    Tidak ada chat aktif. Ambil pesanan untuk memulai chat dengan customer.
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications">
            <div className="space-y-4">
              {notifications.map((notification) => (
                <Card key={notification.id}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{notification.title}</h3>
                        <p className="text-sm text-gray-500">{notification.message}</p>
                      </div>
                      {!notification.is_read && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => notificationService.markAsRead(notification.id)}
                        >
                          Mark as read
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default DriverDashboard;
