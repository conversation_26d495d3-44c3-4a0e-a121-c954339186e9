import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ArrowLeft, MapPin, MessageSquare, Navigation, Phone, Clock } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { driverService } from '../../services/driverService';
import { notificationService } from '../../services/notificationService';
import { OrderList } from './OrderList';
import { Map } from './Map';
import { toast } from 'sonner';

const DriverDashboard = () => {
  const { logout, user } = useAuth();
  const { toast: useToastToast } = useToast();
  const [isOnline, setIsOnline] = useState(false);
  const [currentLocation, setCurrentLocation] = useState(null);
  const [estimatedTime, setEstimatedTime] = useState<string>('');
  const [locationError, setLocationError] = useState<string>('');
  const [availableOrders, setAvailableOrders] = useState([]);
  const [activeOrders, setActiveOrders] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [driverStats, setDriverStats] = useState({
    todayDeliveries: 0,
    todayEarnings: 0,
    avgRating: 4.8,
    totalDistance: 0
  });

  const driverId = user?.id || 'driver-1'; // Use real driver ID from auth

  useEffect(() => {
    loadDashboardData();
    startLocationTracking();
  }, []);

  const loadDashboardData = async () => {
    try {
      console.log('Loading driver dashboard data for:', driverId);

      const [availableData, activeData, notificationData] = await Promise.all([
        driverService.getAvailableOrders(),
        driverService.getActiveOrders(driverId),
        notificationService.getNotifications(driverId)
      ]);

      setAvailableOrders(availableData);
      setActiveOrders(activeData);
      setNotifications(notificationData);

      // Calculate driver stats from real data
      const todayDeliveries = activeData.filter(order =>
        order.status === 'delivered' &&
        new Date(order.updated_at).toDateString() === new Date().toDateString()
      ).length;

      const todayEarnings = activeData
        .filter(order =>
          order.status === 'delivered' &&
          new Date(order.updated_at).toDateString() === new Date().toDateString()
        )
        .reduce((sum, order) => sum + (order.delivery_fee || 8000), 0);

      setDriverStats({
        todayDeliveries,
        todayEarnings,
        avgRating: 4.8,
        totalDistance: todayDeliveries * 2.5 // Estimate 2.5km per delivery
      });

      // Get unread count
      const count = await notificationService.getUnreadCount(driverId);
      setUnreadCount(count);

      console.log('Driver dashboard data loaded:', {
        availableOrders: availableData.length,
        activeOrders: activeData.length,
        notifications: notificationData.length,
        todayDeliveries,
        todayEarnings
      });
    } catch (error) {
      console.error('Error loading driver dashboard data:', error);
      toast.error('Gagal memuat data dashboard');
    }
  };

  const startLocationTracking = () => {
    if ('geolocation' in navigator) {
      navigator.geolocation.watchPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          setCurrentLocation({ latitude, longitude });

          if (isOnline) {
            try {
              await driverService.updateLocation(driverId, {
                latitude,
                longitude,
                is_online: true
              });
            } catch (error) {
              console.error('Failed to update location:', error);
            }
          }
        },
        (error) => {
          console.error('Error getting location:', error);
          useToastToast.error('Failed to get location');
        },
        {
          enableHighAccuracy: true,
          maximumAge: 0,
          timeout: 5000
        }
      );
    }
  };

  const handleToggleOnline = async () => {
    try {
      await driverService.updateLocation(driverId, {
        latitude: currentLocation?.latitude || 0,
        longitude: currentLocation?.longitude || 0,
        is_online: !isOnline
      });
      setIsOnline(!isOnline);
      useToastToast.success(isOnline ? 'You are now offline' : 'You are now online');
    } catch (error) {
      useToastToast.error('Failed to update status');
    }
  };

  const handleAcceptOrder = async (order) => {
    try {
      console.log('Accepting order:', order.id);
      await driverService.acceptOrder(order.id, driverId);

      toast.success(`Pesanan #${order.id} berhasil diterima!`);

      // Reload data to get updated lists
      loadDashboardData();
    } catch (error) {
      console.error('Error accepting order:', error);
      toast.error('Gagal menerima pesanan');
    }
  };

  const handleUpdateOrderStatus = async (orderId, status) => {
    try {
      console.log('Updating order status:', { orderId, status });

      const response = await fetch('http://localhost:5000/api/driver/update-order-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          orderId,
          status,
          driverId
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update order status');
      }

      const statusText = status === 'delivered' ? 'Selesai' :
                        status === 'delivering' ? 'Sedang Diantar' : status;

      toast.success(`Status pesanan #${orderId} berhasil diupdate ke ${statusText}`);

      // Reload data to get updated lists
      loadDashboardData();
    } catch (error) {
      console.error('Error updating order status:', error);
      toast.error('Gagal mengupdate status pesanan');
    }
  };

  const handleUpdateOrderTracking = async (orderId, data) => {
    try {
      await driverService.updateOrderTracking(orderId, {
        ...data,
        latitude: currentLocation?.latitude,
        longitude: currentLocation?.longitude
      });
      useToastToast.success('Order tracking updated successfully');
    } catch (error) {
      useToastToast.error('Failed to update order tracking');
    }
  };

  const calculateETA = (coords: GeolocationCoordinates) => {
    // Simulasi perhitungan ETA (dalam implementasi nyata, gunakan API routing)
    const randomMinutes = Math.floor(Math.random() * 10) + 5;
    setEstimatedTime(`${randomMinutes} menit`);
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('id-ID', {
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Driver Dashboard</h1>
            <p className="text-gray-600">Driver Budi - Motor Honda Beat</p>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm">Status:</span>
              <Button
                onClick={handleToggleOnline}
                variant={isOnline ? "default" : "outline"}
                className={isOnline ? "bg-green-600 hover:bg-green-700" : ""}
              >
                {isOnline ? 'Online' : 'Offline'}
              </Button>
            </div>
            <Button onClick={logout} variant="outline" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Kembali
            </Button>
          </div>
        </div>

        {/* Driver Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-green-100">Delivery Hari Ini</p>
                <p className="text-3xl font-bold">{driverStats.todayDeliveries}</p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-blue-100">Penghasilan Hari Ini</p>
                <p className="text-3xl font-bold">
                  {new Intl.NumberFormat('id-ID', {
                    style: 'currency',
                    currency: 'IDR',
                    minimumFractionDigits: 0,
                  }).format(driverStats.todayEarnings)}
                </p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-orange-100">Rating</p>
                <p className="text-3xl font-bold">{driverStats.avgRating}</p>
              </div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="text-center">
                <p className="text-purple-100">Jarak Tempuh</p>
                <p className="text-3xl font-bold">{driverStats.totalDistance.toFixed(1)} km</p>
                <p className="text-sm text-purple-100">Hari ini</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="orders" className="space-y-6">
          <TabsList className="grid grid-cols-4 w-full max-w-md">
            <TabsTrigger value="orders">Pesanan</TabsTrigger>
            <TabsTrigger value="active">Aktif</TabsTrigger>
            <TabsTrigger value="location">Lokasi</TabsTrigger>
            <TabsTrigger value="chat">Chat</TabsTrigger>
            <TabsTrigger value="notifications">
              Notifications
              {unreadCount > 0 && (
                <span className="ml-2 bg-red-500 text-white rounded-full px-2 py-1 text-xs">
                  {unreadCount}
                </span>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="orders" className="space-y-6">
            {!isOnline ? (
              <Card>
                <CardContent className="p-12 text-center">
                  <p className="text-xl text-gray-500 mb-4">Anda sedang offline</p>
                  <p className="text-gray-400">Aktifkan status online untuk melihat pesanan</p>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Pesanan Tersedia
                  </CardTitle>
                  <CardDescription>Pilih pesanan yang ingin Anda ambil</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <OrderList
                      orders={availableOrders}
                      onAccept={handleAcceptOrder}
                      showAcceptButton
                    />
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="active" className="space-y-6">
            {activeOrders.length > 0 ? (
              <Card>
                <CardHeader>
                  <CardTitle>Pesanan Aktif</CardTitle>
                  <CardDescription>
                    Pilih pesanan untuk mengupdate status
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <OrderList
                    orders={activeOrders}
                    onUpdateStatus={handleUpdateOrderStatus}
                    onUpdateTracking={handleUpdateOrderTracking}
                    showTracking
                  />
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-12 text-center">
                  <p className="text-xl text-gray-500 mb-4">Tidak ada pesanan aktif</p>
                  <p className="text-gray-400">Ambil pesanan dari tab "Pesanan" untuk memulai</p>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="location" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Live Location Tracking
                </CardTitle>
                <CardDescription>Lokasi Anda saat ini</CardDescription>
              </CardHeader>
              <CardContent>
                {locationError ? (
                  <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center">
                    <p className="text-red-600">{locationError}</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="bg-gray-100 rounded-lg p-8 text-center">
                      <MapPin className="h-12 w-12 mx-auto mb-4 text-blue-500" />
                      <p className="text-lg font-medium mb-2">Lokasi Saat Ini</p>
                      <p className="text-gray-600">
                        Lat: {currentLocation?.latitude.toFixed(6)},
                        Lng: {currentLocation?.longitude.toFixed(6)}
                      </p>
                    </div>
                    <div className="text-center">
                      <Button
                        variant="outline"
                        onClick={() => {
                          // Buka Google Maps dengan lokasi saat ini
                          window.open(
                            `https://www.google.com/maps?q=${currentLocation?.latitude},${currentLocation?.longitude}`,
                            '_blank'
                          );
                        }}
                      >
                        Buka di Google Maps
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="chat" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Chat dengan Customer
                </CardTitle>
              </CardHeader>
              <CardContent>
                {activeOrders.length > 0 ? (
                  <div className="space-y-4">
                    <div className="border rounded-lg p-4 bg-gray-50 h-[400px] overflow-y-auto">
                      <p className="text-sm text-gray-600 mb-4">Chat dengan {activeOrders[0].customer}</p>
                      <div className="space-y-4">
                        {/* Chat messages will be populated here */}
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        placeholder="Ketik pesan..."
                        className="flex-1 p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <Button>
                        Kirim
                      </Button>
                    </div>
                  </div>
                ) : (
                  <p className="text-center text-gray-500 py-8">
                    Tidak ada chat aktif. Ambil pesanan untuk memulai chat dengan customer.
                  </p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notifications">
            <div className="space-y-4">
              {notifications.map((notification) => (
                <Card key={notification.id}>
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{notification.title}</h3>
                        <p className="text-sm text-gray-500">{notification.message}</p>
                      </div>
                      {!notification.is_read && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => notificationService.markAsRead(notification.id)}
                        >
                          Mark as read
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default DriverDashboard;
