const express = require('express');
const cors = require('cors');
const dotenv = require('dotenv');
const path = require('path');
const { db, createTables } = require('./database-setup');
const { seedData } = require('./seed-data');

dotenv.config();

const app = express();

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Serve static files from the public directory
app.use(express.static(path.join(__dirname, '../public')));

// Basic route
app.get('/', (req, res) => {
  res.json({ message: 'Welcome to FoodFlow Connect API (SQLite)' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// Initialize database
const initializeDatabase = async () => {
  try {
    await createTables();
    console.log('Database tables created successfully');

    // Check if data already exists
    db.get('SELECT COUNT(*) as count FROM users', (err, row) => {
      if (err) {
        console.error('Error checking data:', err);
        return;
      }

      if (row.count === 0) {
        console.log('No data found, seeding database...');
        seedData().then(() => {
          console.log('Database seeded successfully');
        }).catch(err => {
          console.error('Error seeding database:', err);
        });
      } else {
        console.log('Database already contains data');
      }
    });
  } catch (error) {
    console.error('Error initializing database:', error);
  }
};

// Keep server alive
process.on('SIGINT', () => {
  console.log('Shutting down server...');
  db.close((err) => {
    if (err) {
      console.error('Error closing database:', err);
    } else {
      console.log('Database connection closed.');
    }
    process.exit(0);
  });
});

// Helper function to run SQLite queries with promises
const runQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
};

// Helper function for single row queries
const getQuery = (sql, params = []) => {
  return new Promise((resolve, reject) => {
    db.get(sql, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
};

// Merchant Reviews API
app.get('/api/merchant/reviews', async (req, res) => {
  try {
    const { merchantId } = req.query;
    console.log('Fetching reviews for merchant:', merchantId);

    if (!merchantId) {
      return res.status(400).json({ error: 'Merchant ID is required' });
    }

    // Check if merchant exists
    const merchant = await getQuery(
      'SELECT id FROM users WHERE id = ? AND role = ?',
      [merchantId, 'merchant']
    );

    if (!merchant) {
      return res.status(404).json({ error: 'Merchant not found' });
    }

    const reviews = await runQuery(`
      SELECT
        mr.*,
        u.name as customer_name,
        u.profile_image as customer_avatar,
        t.id as transaction_id,
        t.created_at as order_date
      FROM merchant_reviews mr
      LEFT JOIN users u ON mr.user_id = u.id
      LEFT JOIN transactions t ON mr.transaction_id = t.id
      WHERE mr.merchant_id = ?
      ORDER BY mr.created_at DESC
    `, [merchantId]);

    console.log('Found reviews:', reviews.length);
    res.json(reviews || []);
  } catch (error) {
    console.error('Error in /api/merchant/reviews:', error);
    res.status(500).json({
      error: 'Failed to fetch merchant reviews',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Merchant Menu API
app.get('/api/merchant/menu', async (req, res) => {
  try {
    const { merchantId } = req.query;
    console.log('Fetching menu for merchant:', merchantId);

    const menu = await runQuery(`
      SELECT * FROM menu_items
      WHERE merchant_id = ?
      ORDER BY created_at DESC
    `, [merchantId]);

    console.log('Found menu items:', menu.length);
    res.json(menu);
  } catch (error) {
    console.error('Error in /api/merchant/menu:', error);
    res.status(500).json({ error: error.message });
  }
});

// Merchant Orders API
app.get('/api/merchant/orders', async (req, res) => {
  try {
    const { merchantId } = req.query;
    console.log('Fetching orders for merchant:', merchantId);

    const orders = await runQuery(`
      SELECT
        t.*,
        u.name as customer_name,
        u.phone as customer_phone,
        d.name as driver_name,
        d.phone as driver_phone
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      LEFT JOIN users d ON t.driver_id = d.id
      WHERE t.merchant_id = ?
      ORDER BY t.created_at DESC
    `, [merchantId]);

    console.log('Found orders:', orders.length);
    res.json(orders);
  } catch (error) {
    console.error('Error in /api/merchant/orders:', error);
    res.status(500).json({ error: error.message });
  }
});

// Merchant Categories API
app.get('/api/merchant/categories', async (req, res) => {
  try {
    const { merchantId } = req.query;
    console.log('Fetching categories for merchant:', merchantId);

    const categories = await runQuery(`
      SELECT * FROM menu_categories
      WHERE merchant_id = ?
      ORDER BY name ASC
    `, [merchantId]);

    console.log('Found categories:', categories.length);
    res.json(categories);
  } catch (error) {
    console.error('Error in /api/merchant/categories:', error);
    res.status(500).json({ error: error.message });
  }
});

// Operating Hours API
app.get('/api/merchant/operating-hours', async (req, res) => {
  try {
    const { merchantId } = req.query;
    console.log('Fetching operating hours for merchant:', merchantId);

    const hours = await runQuery(`
      SELECT * FROM operating_hours
      WHERE merchant_id = ?
      ORDER BY
        CASE day_of_week
          WHEN 'monday' THEN 1
          WHEN 'tuesday' THEN 2
          WHEN 'wednesday' THEN 3
          WHEN 'thursday' THEN 4
          WHEN 'friday' THEN 5
          WHEN 'saturday' THEN 6
          WHEN 'sunday' THEN 7
        END
    `, [merchantId]);

    console.log('Found operating hours:', hours.length);
    res.json(hours);
  } catch (error) {
    console.error('Error in /api/merchant/operating-hours:', error);
    res.status(500).json({ error: error.message });
  }
});

// Notifications API
app.get('/api/notifications', async (req, res) => {
  try {
    const { userId } = req.query;
    console.log('Fetching notifications for user:', userId);

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const notifications = await runQuery(`
      SELECT * FROM notifications
      WHERE user_id = ?
      ORDER BY created_at DESC
    `, [userId]);

    console.log('Found notifications:', notifications.length);
    res.json(notifications);
  } catch (error) {
    console.error('Error in /api/notifications:', error);
    res.status(500).json({
      error: 'Failed to fetch notifications',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

app.get('/api/notifications/unread-count', async (req, res) => {
  try {
    const { userId } = req.query;
    console.log('Fetching unread count for user:', userId);

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const result = await getQuery(`
      SELECT COUNT(*) as unread_count FROM notifications
      WHERE user_id = ? AND is_read = 0
    `, [userId]);

    const unreadCount = result ? result.unread_count : 0;
    console.log('Unread count:', unreadCount);
    res.json(unreadCount);
  } catch (error) {
    console.error('Error in /api/notifications/unread-count:', error);
    res.status(500).json({
      error: 'Failed to fetch unread count',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Merchant Analytics API
app.get('/api/merchant/analytics', async (req, res) => {
  try {
    const { merchantId } = req.query;
    console.log('Fetching merchant analytics for:', merchantId);

    if (!merchantId) {
      return res.status(400).json({ error: 'Merchant ID is required' });
    }

    // Get total orders and revenue for last 30 days
    const monthlyStats = await getQuery(`
      SELECT
        COUNT(*) as total_orders,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(AVG(
          CASE
            WHEN actual_delivery_time IS NOT NULL AND created_at IS NOT NULL
            THEN (julianday(actual_delivery_time) - julianday(created_at)) * 24 * 60
            ELSE NULL
          END
        ), 0) as avg_preparation_time
      FROM transactions
      WHERE merchant_id = ?
      AND created_at >= datetime('now', '-30 days')
      AND status = 'delivered'
    `, [merchantId]);

    // Get previous month stats for comparison
    const previousStats = await getQuery(`
      SELECT
        COUNT(*) as prev_orders,
        COALESCE(SUM(total_amount), 0) as prev_revenue
      FROM transactions
      WHERE merchant_id = ?
      AND created_at >= datetime('now', '-60 days')
      AND created_at < datetime('now', '-30 days')
      AND status = 'delivered'
    `, [merchantId]);

    // Get popular menu items
    const popularItems = await runQuery(`
      SELECT
        ti.menu_name as name,
        SUM(ti.quantity) as order_count
      FROM transaction_items ti
      JOIN transactions t ON ti.transaction_id = t.id
      WHERE t.merchant_id = ?
      AND t.created_at >= datetime('now', '-30 days')
      AND t.status = 'delivered'
      GROUP BY ti.menu_name
      ORDER BY order_count DESC
      LIMIT 5
    `, [merchantId]);

    // Calculate percentage changes
    const orderChange = previousStats && previousStats.prev_orders > 0
      ? (((monthlyStats.total_orders - previousStats.prev_orders) / previousStats.prev_orders) * 100).toFixed(1)
      : '+0.0';

    const revenueChange = previousStats && previousStats.prev_revenue > 0
      ? (((monthlyStats.total_revenue - previousStats.prev_revenue) / previousStats.prev_revenue) * 100).toFixed(1)
      : '+0.0';

    const response = {
      totalOrders: monthlyStats.total_orders || 0,
      orderChange: orderChange.startsWith('-') ? orderChange : `+${orderChange}`,
      monthlyRevenue: monthlyStats.total_revenue || 0,
      revenueChange: revenueChange.startsWith('-') ? revenueChange : `+${revenueChange}`,
      popularItems: popularItems || [],
      avgPreparationTime: Math.round(monthlyStats.avg_preparation_time || 0),
      prepTimeChange: '-2.0' // Mock value for now
    };

    console.log('Merchant analytics response:', response);
    res.json(response);
  } catch (error) {
    console.error('Error in /api/merchant/analytics:', error);
    res.status(500).json({
      error: 'Failed to fetch merchant analytics',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
  }
});

// Initialize database and start server
initializeDatabase();

const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
  console.log('Environment:', process.env.NODE_ENV || 'development');
  console.log('Using SQLite database: foodflow.db');
  console.log('Available API endpoints:');
  console.log('- GET /api/merchant/reviews?merchantId=merchant-1');
  console.log('- GET /api/notifications?userId=merchant-1');
  console.log('- GET /api/notifications/unread-count?userId=merchant-1');
  console.log('- GET /api/merchant/menu?merchantId=merchant-1');
  console.log('- GET /api/merchant/orders?merchantId=merchant-1');
  console.log('- GET /api/merchant/categories?merchantId=merchant-1');
  console.log('- GET /api/merchant/operating-hours?merchantId=merchant-1');
});
