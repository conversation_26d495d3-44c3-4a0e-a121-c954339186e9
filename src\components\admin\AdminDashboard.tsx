import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import PriceSettingsDialog from './PriceSettingsDialog';
import { adminService, SystemSettings } from '@/services/adminService';
import {
  Users,
  Store,
  Car,
  DollarSign,
  CheckCircle,
  XCircle,
  Clock,
  Phone,
  Mail,
  Building,
  Truck,
  Settings,
  Search,
  Filter,
  Download,
  Ban
} from 'lucide-react';
import { User } from '@/types';

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const { toast } = useToast();
  const [showPriceSettings, setShowPriceSettings] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<User['role'] | 'all'>('all');
  const [timeRange, setTimeRange] = useState('7d');

  // State for real data
  const [stats, setStats] = useState({
    totalUsers: 0,
    totalMerchants: 0,
    totalDrivers: 0,
    totalTransactions: 0,
    totalRevenue: 0
  });
  const [pendingUsers, setPendingUsers] = useState([]);
  const [users, setUsers] = useState([]);
  const [recentTransactions, setRecentTransactions] = useState([]);
  const [activityLogs, setActivityLogs] = useState([]);
  const [systemSettings, setSystemSettings] = useState<SystemSettings>({
    price_markup_percentage: 0,
    delivery_base_rate: 0,
    merchant_commission_percentage: 0,
    driver_commission_percentage: 0
  });

  // Load initial data
  useEffect(() => {
    loadDashboardData();
  }, []);

  // Load filtered users when search or role filter changes
  useEffect(() => {
    loadUsers();
  }, [searchQuery, roleFilter]);

  const loadDashboardData = async () => {
    try {
      const [statsData, pendingUsersData, transactionsData, logsData, settingsData] = await Promise.all([
        adminService.getDashboardStats(),
        adminService.getPendingUsers(),
        adminService.getRecentTransactions(),
        adminService.getActivityLogs(),
        adminService.getSystemSettings()
      ]);

      setStats(statsData);
      setPendingUsers(pendingUsersData);
      setRecentTransactions(transactionsData);
      setActivityLogs(logsData);
      setSystemSettings(settingsData);
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal memuat data dashboard",
        variant: "destructive"
      });
    }
  };

  const loadUsers = async () => {
    try {
      const usersData = await adminService.getUsers(searchQuery, roleFilter);
      setUsers(usersData);
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal memuat data user",
        variant: "destructive"
      });
    }
  };

  const handleApprove = async (userId: string, userName: string) => {
    try {
      await adminService.approveUser(userId);
      await loadDashboardData();
      toast({
        title: "User Disetujui",
        description: `${userName} telah disetujui dan dapat menggunakan aplikasi.`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal menyetujui user",
        variant: "destructive"
      });
    }
  };

  const handleReject = async (userId: string, userName: string) => {
    try {
      await adminService.rejectUser(userId);
      await loadDashboardData();
      toast({
        title: "User Ditolak",
        description: `Pendaftaran ${userName} telah ditolak.`,
        variant: "destructive",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal menolak user",
        variant: "destructive"
      });
    }
  };

  const handleSuspendUser = async (userId: string, userName: string) => {
    try {
      await adminService.suspendUser(userId);
      await loadUsers();
      toast({
        title: "User Ditangguhkan",
        description: `${userName} telah ditangguhkan dari sistem.`,
        variant: "destructive",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal menangguhkan user",
        variant: "destructive"
      });
    }
  };

  const handleExportData = async () => {
    try {
      // Implement export logic here
      toast({
        title: "Data Diekspor",
        description: "Data telah berhasil diekspor ke CSV.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal mengekspor data",
        variant: "destructive"
      });
    }
  };

  const formatCurrency = (value: number | undefined | null) => {
    const numValue = Number(value) || 0;
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(numValue);
  };

  const getRoleIcon = (role: User['role']) => {
    switch (role) {
      case 'merchant':
        return <Building className="h-4 w-4" />;
      case 'driver':
        return <Truck className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const getRoleBadgeColor = (role: User['role']) => {
    switch (role) {
      case 'merchant':
        return 'bg-blue-100 text-blue-800';
      case 'driver':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Admin Dashboard</h1>
              <p className="text-gray-600">Selamat datang, {user?.name}</p>
            </div>
            <div className="flex items-center gap-4">
              <Button variant="outline" onClick={handleExportData}>
                <Download className="h-4 w-4 mr-2" />
                Export Data
              </Button>
              <Button variant="outline" onClick={logout}>
                Logout
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalUsers}</p>
                </div>
                <div className="p-3 rounded-full bg-blue-100">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Merchants</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalMerchants}</p>
                </div>
                <div className="p-3 rounded-full bg-green-100">
                  <Store className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Drivers</p>
                  <p className="text-2xl font-bold text-gray-900">{stats.totalDrivers}</p>
                </div>
                <div className="p-3 rounded-full bg-purple-100">
                  <Car className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalRevenue)}</p>
                </div>
                <div className="p-3 rounded-full bg-orange-100">
                  <DollarSign className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* User Management */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  User Management
                </CardTitle>
                <CardDescription>
                  Kelola semua user dalam sistem
                </CardDescription>
              </div>
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Cari user..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
                <Select value={roleFilter} onValueChange={(value) => setRoleFilter(value as User['role'] | 'all')}>
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Role</SelectItem>
                    <SelectItem value="customer">Customer</SelectItem>
                    <SelectItem value="merchant">Merchant</SelectItem>
                    <SelectItem value="driver">Driver</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {users.map((user) => (
                <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg bg-white">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className="flex items-center gap-2">
                        {getRoleIcon(user.role)}
                        <h3 className="font-semibold">{user.name}</h3>
                      </div>
                      <Badge className={getRoleBadgeColor(user.role)}>
                        {user.role}
                      </Badge>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Mail className="h-4 w-4" />
                        {user.email}
                      </div>
                      <div className="flex items-center gap-1">
                        <Phone className="h-4 w-4" />
                        {user.phone}
                      </div>
                      {user.businessName && (
                        <div className="flex items-center gap-1">
                          <Building className="h-4 w-4" />
                          {user.businessName}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2 ml-4">
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleSuspendUser(user.id, user.name)}
                    >
                      <Ban className="h-4 w-4 mr-1" />
                      Suspend
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Analytics */}
        <Card className="mb-8">
          <CardHeader>
            <div className="flex justify-between items-center">
              <div>
                <CardTitle>Analytics</CardTitle>
                <CardDescription>
                  Statistik dan performa sistem
                </CardDescription>
              </div>
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="7d">7 Hari Terakhir</SelectItem>
                  <SelectItem value="30d">30 Hari Terakhir</SelectItem>
                  <SelectItem value="90d">90 Hari Terakhir</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="p-6 border rounded-lg bg-white">
                <h3 className="text-lg font-medium mb-2">Total Transaksi</h3>
                <p className="text-3xl font-bold text-blue-600">{stats.totalTransactions}</p>
                <p className="text-sm text-gray-600">+15% dari periode sebelumnya</p>
              </div>
              <div className="p-6 border rounded-lg bg-white">
                <h3 className="text-lg font-medium mb-2">Revenue</h3>
                <p className="text-3xl font-bold text-green-600">{formatCurrency(stats.totalRevenue)}</p>
                <p className="text-sm text-gray-600">+22% dari periode sebelumnya</p>
              </div>
              <div className="p-6 border rounded-lg bg-white">
                <h3 className="text-lg font-medium mb-2">User Baru</h3>
                <p className="text-3xl font-bold text-purple-600">{stats.totalMerchants}</p>
                <p className="text-sm text-gray-600">+8% dari periode sebelumnya</p>
              </div>
              <div className="p-6 border rounded-lg bg-white">
                <h3 className="text-lg font-medium mb-2">Rating Rata-rata</h3>
                <p className="text-3xl font-bold text-orange-600">4.8</p>
                <p className="text-sm text-gray-600">+0.2 dari periode sebelumnya</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pending Approvals */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-600" />
              Pending Approvals ({pendingUsers.length})
            </CardTitle>
            <CardDescription>
              User yang menunggu persetujuan untuk bergabung
            </CardDescription>
          </CardHeader>
          <CardContent>
            {pendingUsers.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                <p className="text-gray-600">Tidak ada pending approval</p>
              </div>
            ) : (
              <div className="space-y-4">
                {pendingUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg bg-white">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <div className="flex items-center gap-2">
                          {getRoleIcon(user.role)}
                          <h3 className="font-semibold">{user.name}</h3>
                        </div>
                        <Badge className={getRoleBadgeColor(user.role)}>
                          {user.role === 'merchant' ? 'Pemilik Toko' : 'Driver'}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Mail className="h-4 w-4" />
                          {user.email}
                        </div>
                        <div className="flex items-center gap-1">
                          <Phone className="h-4 w-4" />
                          {user.phone}
                        </div>
                        {user.businessName && (
                          <div className="flex items-center gap-1">
                            <Building className="h-4 w-4" />
                            {user.businessName}
                          </div>
                        )}
                        {user.vehicleType && (
                          <div className="flex items-center gap-1">
                            <Truck className="h-4 w-4" />
                            {user.vehicleType} - {user.vehicleNumber}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex gap-2 ml-4">
                      <Button
                        size="sm"
                        onClick={() => handleApprove(user.id, user.name)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Setujui
                      </Button>
                      <Button
                        size="sm"
                        variant="destructive"
                        onClick={() => handleReject(user.id, user.name)}
                      >
                        <XCircle className="h-4 w-4 mr-1" />
                        Tolak
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Settings and Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Pengaturan Tarif & Komisi
              </CardTitle>
              <CardDescription>
                Kelola markup harga dan komisi sistem
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span>Markup harga customer</span>
                  <span className="font-semibold">{systemSettings.price_markup_percentage || 0}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Tarif dasar pengiriman</span>
                  <span className="font-semibold">{formatCurrency(systemSettings.delivery_base_rate || 0)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Komisi merchant</span>
                  <span className="font-semibold">{systemSettings.merchant_commission_percentage || 0}%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span>Komisi driver</span>
                  <span className="font-semibold">{systemSettings.driver_commission_percentage || 0}%</span>
                </div>
                <Button
                  className="w-full"
                  variant="outline"
                  onClick={() => setShowPriceSettings(true)}
                >
                  <Settings className="h-4 w-4 mr-2" />
                  Edit Pengaturan
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Aktivitas Terbaru</CardTitle>
              <CardDescription>
                Transaksi dan aktivitas sistem
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span>Order #1234 completed</span>
                  <span className="text-gray-500 ml-auto">2 min ago</span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span>New merchant registered</span>
                  <span className="text-gray-500 ml-auto">5 min ago</span>
                </div>
                <div className="flex items-center gap-3 text-sm">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <span>Driver went online</span>
                  <span className="text-gray-500 ml-auto">10 min ago</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <PriceSettingsDialog
        open={showPriceSettings}
        onOpenChange={setShowPriceSettings}
        settings={systemSettings}
        onSave={async (newSettings) => {
          try {
            await adminService.updateSystemSettings(newSettings);
            await loadDashboardData();
            toast({
              title: "Pengaturan Diperbarui",
              description: "Pengaturan sistem berhasil diperbarui.",
            });
          } catch (error) {
            toast({
              title: "Error",
              description: "Gagal memperbarui pengaturan",
              variant: "destructive"
            });
          }
        }}
      />
    </div>
  );
};

export default AdminDashboard;
