
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { SystemSettings } from '@/services/adminService';

interface PriceSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  settings: SystemSettings;
  onSave: (settings: SystemSettings) => Promise<void>;
}

const PriceSettingsDialog = ({ open, onOpenChange, settings: initialSettings, onSave }: PriceSettingsDialogProps) => {
  const { toast } = useToast();

  const [settings, setSettings] = useState<SystemSettings>({
    price_markup_percentage: 0,
    delivery_base_rate: 0,
    merchant_commission_percentage: 0,
    driver_commission_percentage: 0
  });

  // Update local state when props change
  useEffect(() => {
    setSettings(initialSettings);
  }, [initialSettings]);

  const handleSave = async () => {
    try {
      await onSave(settings);
      toast({
        title: "Pengaturan Disimpan",
        description: "Pengaturan tarif telah berhasil diperbarui.",
      });
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Gagal menyimpan pengaturan",
        variant: "destructive"
      });
    }
  };

  const formatCurrency = (value: number | undefined | null) => {
    const numValue = Number(value) || 0;
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(numValue);
  };

  // Example calculation
  const exampleMerchantPrice = 10000;
  const exampleCustomerPrice = exampleMerchantPrice + (exampleMerchantPrice * (settings.price_markup_percentage / 100));

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Pengaturan Tarif & Komisi</DialogTitle>
          <DialogDescription>
            Kelola markup harga dan komisi untuk seluruh sistem
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Price Markup */}
          <div className="space-y-2">
            <Label htmlFor="priceMarkup">Markup Harga untuk Customer (%)</Label>
            <Input
              id="priceMarkup"
              type="number"
              step="0.1"
              value={settings.price_markup_percentage}
              onChange={(e) => setSettings({...settings, price_markup_percentage: parseFloat(e.target.value) || 0})}
            />
            <p className="text-sm text-gray-600">
              Contoh: Harga merchant {formatCurrency(exampleMerchantPrice)} →
              Customer bayar {formatCurrency(exampleCustomerPrice)}
            </p>
          </div>

          {/* Delivery Base Rate */}
          <div className="space-y-2">
            <Label htmlFor="deliveryRate">Tarif Dasar Pengiriman (Rp)</Label>
            <Input
              id="deliveryRate"
              type="number"
              value={settings.delivery_base_rate}
              onChange={(e) => setSettings({...settings, delivery_base_rate: parseInt(e.target.value) || 0})}
            />
          </div>

          {/* Merchant Commission */}
          <div className="space-y-2">
            <Label htmlFor="merchantCommission">Komisi Merchant (%)</Label>
            <Input
              id="merchantCommission"
              type="number"
              step="0.1"
              value={settings.merchant_commission_percentage}
              onChange={(e) => setSettings({...settings, merchant_commission_percentage: parseFloat(e.target.value) || 0})}
            />
            <p className="text-sm text-gray-600">
              Persentase yang dipotong dari penjualan merchant
            </p>
          </div>

          {/* Driver Commission */}
          <div className="space-y-2">
            <Label htmlFor="driverCommission">Komisi Driver (%)</Label>
            <Input
              id="driverCommission"
              type="number"
              step="0.1"
              value={settings.driver_commission_percentage}
              onChange={(e) => setSettings({...settings, driver_commission_percentage: parseFloat(e.target.value) || 0})}
            />
            <p className="text-sm text-gray-600">
              Persentase dari ongkir yang diterima driver
            </p>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Batal
          </Button>
          <Button onClick={handleSave}>
            Simpan Pengaturan
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PriceSettingsDialog;
